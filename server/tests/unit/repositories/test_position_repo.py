from logging import Logger
import pytest
from unittest.mock import Magic<PERSON><PERSON>, Mo<PERSON>, patch

from repositories.position_repo import PositionRepository

# get_session_idをモック化
@pytest.fixture
def mock_get_session_id():
    with patch('repositories.position_repo.get_session_id', return_value='test_session_id') as mock:
        yield mock


# loggerをモック化
@pytest.fixture
def mock_logger():
    return Mock(spec=Logger)

def test_save_position_detail_success(mock_get_session_id, mock_logger):
    """
    正常にポジション詳細がキャッシュされることをテストする
    """
    # PositionRepositoryのインスタンスを生成し、依存を注入
    repository = PositionRepository()
    repository._logger = mock_logger

    # decrypt_id メソッドが特定の値を返すようにモック化
    with patch.object(repository, 'decrypt_id', return_value='decrypted_position_id') as mock_decrypt_id:

        # テスト実行
        encrypted_position_id = "encrypted_position_id"
        position_data = {"title": "Test Position", "salary": 500}
        repository.save_position_detail(encrypted_position_id, position_data)

        # decrypt_idが呼び出されたことを確認
        mock_decrypt_id.assert_called_once_with(encrypted_position_id)

        # get_session_idが呼び出されたことを確認
        mock_get_session_id.assert_called_once()

        # ポジションデータがキャッシュに保存されたことを確認
        cached_data = repository._position_details['test_session_id']['decrypted_position_id']
        assert cached_data == position_data

        # 例外が発生しなかったため、ロガーが呼び出されていないことを確認
        mock_logger.exception.assert_not_called()

def test_save_position_detail_exception(mock_get_session_id, mock_logger):
    """
    ポジションIDの復号化に失敗した場合、ロガーが呼び出されることをテストする
    """
    # PositionRepositoryのインスタンスを生成し、依存を注入
    repository = PositionRepository()
    repository._logger = mock_logger
    
    # decrypt_id メソッドが例外を発生させるようにモック化
    with patch.object(repository, 'decrypt_id', side_effect=Exception("Decryption failed")) as mock_decrypt_id:
        
        # テスト実行
        encrypted_position_id = "invalid_encrypted_position_id"
        position_data = {"title": "Should Not Be Saved"}
        repository.save_position_detail(encrypted_position_id, position_data)
        
        # decrypt_idが呼び出されたことを確認
        mock_decrypt_id.assert_called_once_with(encrypted_position_id)

        # get_session_idが呼び出されていないことを確認
        mock_get_session_id.assert_not_called()

        # 例外が発生したため、ロガーが呼び出されたことを確認
        mock_logger.exception.assert_called_once_with("ポジションID復号化: %s", encrypted_position_id)

        # 例外発生により、キャッシュが保存されていないことを確認
        assert 'test_session_id' not in repository._position_details
