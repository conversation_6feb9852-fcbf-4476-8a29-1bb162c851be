"""
Websocket、Restful APIエンドポイントポ
"""

import json
import logging
import os
import uuid
from agents import trace
from dependency_injector.wiring import Provide, inject
from functools import partial
from fastapi import (
    APIRouter,
    Depends,
    WebSocket,
    WebSocketDisconnect,
    Query,
    Request,
    status,
)
from fastapi.responses import JSONResponse
from typing import Optional

from pydantic import ValidationError


from containers import Container
from domain.entities.chat_session import ChatSessionStatus
from domain.entities.user_profile import (
    JSONSearchLocation,
    JSONSearchKeyword,
    JSONUserProfileBasicInfo,
    JSONUserProfileCarrer,
    JSONUserProfileEducation,
    JSONUserProfileWill,
)
from services.chat_service import ChatService, ChatStreamResponse
from services.position_service import PositionService
from services.user_service import UserService
from utils.chat_request import ChatRequestModel, ChatRequestType
from utils.const import API_PREFIX, LOGGER_PREFIX, MAINTENANCE_MESSAGE
from utils.enum import PageName
from utils.fastapi.dependency import (
    source_component_analysis,
    session_status_validator,
)
from utils.logging import get_session_id, set_session_id, clear_session_id

logger = logging.getLogger(f"{LOGGER_PREFIX}.{__name__}")
router = APIRouter(prefix=API_PREFIX)


@router.get("/health")
async def health():
    """
    ヘルスチェックエンドポイント
    Returns:
        OK
    """
    return {"status": "OK"}


@router.get(
    "/positions/search/{search_key}/{offset}",
    dependencies=[
        Depends(
            partial(
                session_status_validator,
                required_session_statuses=[
                    ChatSessionStatus.CHATTING,
                    ChatSessionStatus.APPLIED,
                    ChatSessionStatus.REGISTERED,
                ],
            ),
        ),
    ],
)
@inject
async def load_more_positions(
    search_key: str,
    offset: int,
    limit: int = 5,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    """
    求人検索結果のもっと見る
    Args:
        search_key (str): 検索キー。複数検索があるかもしれないので、区別のため
        offset (int): 読み込み開始位置
        limit (int): 読み込み件数上限（デフォルト: 5）
        position_service: PositionServiceインスタンス
    Returns:
        dict: 求人総数と求人リスト
    """
    # 分析用のログ出力
    logger.info("analyze_load_more_positions")

    count, positions = await position_service.load_more(
        search_key,
        offset,
        limit,
    )
    return {
        "TotalPositionCount": count,
        "Positions": positions,
    }


@router.get(
    "/positions/detail/{encrypted_position_id}",
    dependencies=[
        Depends(
            source_component_analysis,
        ),
    ],
)
@inject
async def position_detail(
    encrypted_position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    """
    求人詳細情報取得
    Args:
        encrypted_position_id (str): 暗号化された求人ID
        position_service: PositionServiceインスタンス
    Returns:
        dict: 求人詳細情報、見つからない場合は404エラー
    """
    detail = await position_service.get_position_detail(encrypted_position_id)
    if detail:
        return detail
    else:
        # HTTPException利用の場合、同時にたくさんリクエストが来ている場合、サーバーが死んでしまうので、代わりにJSONResponseを利用します
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )


@router.get(
    "/companies/detail/{encrypted_position_id}",
)
@inject
async def company_detail(
    encrypted_position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    """
    企業詳細情報取得
    Args:
        encrypted_position_id (str): 暗号化された求人ID
        position_service: PositionServiceインスタンス
    Returns:
        dict: 企業詳細情報、見つからない場合は404エラー
    """
    detail = await position_service.get_company_detail(encrypted_position_id)
    if detail:
        return detail
    else:
        # Use JSONResponse instead of HTTPException to avoid concurrency issues
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )


@router.get(
    "/businesses/detail/{encrypted_position_id}",
)
@inject
async def business_detail(
    encrypted_position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    """
    事業詳細情報取得
    Args:
        encrypted_position_id (str): 暗号化された求人ID
        position_service: PositionServiceインスタンス
    Returns:
        dict: 事業詳細情報、見つからない場合は404エラー
    """
    detail = await position_service.get_business_detail(encrypted_position_id)
    if detail:
        return detail
    else:
        # Use JSONResponse instead of HTTPException to avoid concurrency issues
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )


@router.get(
    "/positions/recommendations/{search_key}/{encrypted_theme}",
)
@inject
async def position_recommendations(
    search_key: str,
    encrypted_theme: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    """
    求人提案取得
    Args:
        search_key (str): 検索キー。複数検索があるかもしれないので、区別のため
        encrypted_theme (str): 暗号化されたテーマ
        position_service: PositionServiceインスタンス
    Returns:
        dict: 検索キーと提案求人リスト
    """
    positions = await position_service.get_position_recommendation(encrypted_theme)

    if not positions:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "SearchKey": search_key,
                "Positions": [],
            },
        )

    return {
        "SearchKey": search_key,
        "Positions": positions,
    }


@router.get(
    "/positions/re-search/{tool_call_id}",
)
@inject
async def position_search_by_tool_call_id(
    tool_call_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    """
    求人提案取得
    Args:
        tool_call_id (str): ツールコールID。
        position_service: PositionServiceインスタンス
    Returns:
        dict: 検索キーと提案求人リスト
    """
    positions = await position_service.search_positions_by_tool_call_id(tool_call_id)

    if not positions:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )

    return positions


@router.get(
    "/master/",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def search_master_data(
    names: list[str] = Query(..., description="取得するマスターデータ名のリスト"),
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    マスターデータ取得
    Args:
        names: 取得するマスターデータ名のリスト
        user_service: UserServiceインスタンス
    Returns:
        dict: マスターデータ、エラーの場合は500エラー
    """
    logger.debug("endpoint search_master_data names: %s", names)
    result = await user_service.search_master_data(names)
    if result is None:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={},
        )
    else:
        return result


@router.post(
    "/location/verify/prefecture/city",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def search_location_by_prefecture_city(
    json_request: JSONSearchLocation,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    指定の都道府県・市区町村検索
    Args:
        json_request: JSONSearchLocationモデル
        user_service: UserServiceインスタンス
    Returns:
        list: 検索結果の都道府県、市区町村リスト、エラーの場合は500エラー
    """
    logger.debug(
        "endpoint search_location_by_prefecture_city %s", json_request.model_dump_json()
    )
    result = await user_service.search_by_prefecture_city_name(
        json_request.prefecture_name,
        json_request.city_name,
    )
    if result is None:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={},
        )
    else:
        return result


@router.post(
    "/location/search/commuting_areas",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def search_commuting_areas(
    json_request: JSONSearchLocation,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    勤務地検索
    Args:
        json_request: JSONSearchLocationモデル
        user_service: UserServiceインスタンス
    Returns:
        list: 検索結果の都道府県、市区町村リスト、エラーの場合は500エラー
    """
    logger.debug("endpoint search_commuting_areas %s", json_request.model_dump_json())
    result = await user_service.search_commuting_areas(
        json_request.location_type,
        json_request.prefecture_name,
        json_request.city_name,
    )
    if result is None:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={},
        )
    else:
        return result


@router.post(
    "/location/search/keyword",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def search_location_by_keyword(
    json_request: JSONSearchKeyword,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    所在地検索
    Args:
        json_request: JSONSearchKeywordモデル
        user_service: UserServiceインスタンス
    Returns:
        list: 検索結果の都道府県、市区町村リスト、エラーの場合は500エラー
    """
    logger.debug(
        "endpoint search_location_by_keyword %s", json_request.model_dump_json()
    )
    result = await user_service.search_location(json_request.keyword)
    if result is None:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={},
        )
    else:
        return result


@router.post(
    "/industry/search/keyword",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def search_industry(
    json_request: JSONSearchKeyword,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    業界検索
    Args:
        json_request: JSONSearchKeywordモデル
        user_service: UserServiceインスタンス
    Returns:
        list: 検索結果の業界リスト、エラーの場合は500エラー
    """
    logger.debug("endpoint search_industry %s", json_request.model_dump_json())
    result = await user_service.search_industry(json_request.keyword)
    if result is None:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={},
        )
    else:
        return result


@router.post(
    "/jobtype/search/keyword",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def search_jobtype(
    json_request: JSONSearchKeyword,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    職種検索
    Args:
        json_request: JSONSearchKeywordモデル
        user_service: UserServiceインスタンス
    Returns:
        list: 検索結果の職種リスト、エラーの場合は500エラー
    """
    logger.debug("endpoint search_jobtype %s", json_request.model_dump_json())
    result = await user_service.search_jobtype(json_request.keyword)
    if result is None:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={},
        )
    else:
        return result


@router.get(
    "/profile/job_filter",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def job_filter(
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    logger.debug("endpoint profile/job_filter")
    result = user_service.get_profile()

    if not result or not result.job_search_filter:
        return {}

    return result.job_search_filter


@router.get(
    "/profile",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def get_profile(
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    logger.debug("endpoint get_profile")
    result = user_service.get_profile()

    if not result or not result.miidas_registration_user_data:
        return {}

    return result.miidas_registration_user_data


@router.post(
    "/profile/basic",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def save_basic_profile(
    basic_profile: JSONUserProfileBasicInfo,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    基本情報保存

    Args:
        basic_profile: 基本情報
        user_service: UserServiceインスタンス

    Returns:
        dict: 保存結果
    """
    logger.debug(
        f"endpoint save_basic_profile {basic_profile.model_dump_json()}",
    )
    result = user_service.save_basic_profile(
        basic_profile,
    )

    if not result:
        # raise HTTPException(status_code=404...) will make the server hang.
        # So instead of raising HTTPException, return a normal response
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "Success": False,
                "Message": "プロフィールの保存に失敗しました",
            },
        )

    return {
        "Success": True,
        "Message": "プロフィールを保存しました",
    }


@router.post(
    "/profile/education",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def save_education_profile(
    education_profile: JSONUserProfileEducation,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    学歴保存

    Args:
        education_profile: 学歴
        user_service: UserServiceインスタンス

    Returns:
        dict: 保存結果
    """
    logger.debug(
        f"endpoint save_education_profile {education_profile.model_dump_json()}",
    )

    result = user_service.save_education_profile(
        education_profile,
    )

    if not result:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "Success": False,
                "Message": "学歴情報の保存に失敗しました",
            },
        )

    return {
        "Success": True,
        "Message": "学歴情報を保存しました",
    }


@router.post(
    "/profile/experience",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def save_experience_profile(
    experience_profile: JSONUserProfileCarrer,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    職歴保存

    Args:
        education_profile: 職歴
        user_service: UserServiceインスタンス

    Returns:
        dict: 保存結果
    """
    logger.debug(
        f"endpoint save_experience_profile {experience_profile.model_dump_json()}",
    )

    result = user_service.save_experience_profile(
        experience_profile,
    )

    if not result:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "Success": False,
                "Message": "職務経歴の保存に失敗しました",
            },
        )

    return {
        "Success": True,
        "Message": "職務経歴を保存しました",
    }


@router.post(
    "/profile/preferences",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def save_preferences_profile(
    preferences_profile: JSONUserProfileWill,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    希望条件保存

    Args:
        education_profile: 希望条件
        user_service: UserServiceインスタンス

    Returns:
        dict: 保存結果
    """
    logger.debug(
        f"endpoint save_preferences_profile {preferences_profile.model_dump_json()}",
    )

    result = user_service.save_preferences_profile(
        preferences_profile,
    )

    if not result:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "Success": False,
                "Message": "希望条件の保存に失敗しました",
            },
        )

    return {
        "Success": True,
        "Message": "希望条件を保存しました",
    }


@router.post(
    "/apply/start",
)
@router.post(
    "/apply/{encrypted_position_id}/start",
)
@inject
async def apply_start(
    encrypted_position_id: str | None = None,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    応募開始

    Args:
        encrypted_position_id: 暗号化されたポジションID
        user_service: UserServiceインスタンス

    Returns:
        チャットセッションステータス
    """
    session_status = user_service.start_apply(encrypted_position_id)
    if session_status:
        return {
            "session_status": session_status,
        }
    else:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )


@router.put(
    "/apply/{encrypted_position_id}/add",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def apply_add(
    encrypted_position_id: str,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    応募ポジション追加

    Args:
        encrypted_position_id: 暗号化されたポジションID
        user_service: UserServiceインスタンス

    Returns:
    """
    session_status = user_service.apply_add_position(encrypted_position_id)
    if session_status:
        return {
            "session_status": session_status,
        }
    else:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )


@router.post(
    "/apply/finish",
    dependencies=[
        Depends(
            session_status_validator,
        ),
    ],
)
@inject
async def finish_apply(
    request: Request,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    面談応募・登録する

    Args:
        user_service: UserServiceインスタンス

    Returns:
        チャットセッションステータス
    """
    http_status, session_status, apply_result, detail = (
        await user_service.finish_apply(
            user_agent=request.headers.get("User-Agent"),
        )
    )
    cookies = detail.pop("Cookies", None) if detail else None

    response = JSONResponse(
        status_code=http_status,
        content={
            "SessionStatus": session_status,
            "ApplyResult": apply_result,
            "Detail": detail,
        },
    )

    if cookies:
        for cookie in cookies:
            response.set_cookie(
                key=cookie.key,
                value=cookie.value,
                max_age=cookie.get("max-age"),
                expires=cookie.get("expires"),
                path=cookie.get("path") or "/",
                domain=cookie.get("domain"),
                secure=cookie.get("secure", False),
                httponly=cookie.get("httponly", False),
                samesite=cookie.get("samesite"),
            )

    return response


@router.post(
    "/apply/{encrypted_position_id}",
    dependencies=[
        Depends(
            partial(
                session_status_validator,
                required_session_statuses=[
                    ChatSessionStatus.APPLIED,
                    ChatSessionStatus.REGISTERED,
                ],
            ),
        ),
    ],
)
@inject
async def apply(
    encrypted_position_id: str,
    user_service: UserService = Depends(Provide[Container.user_service]),
):
    """
    ポジション応募

    Args:
        encrypted_position_id: 暗号化されたポジションID（オプション）
        user_service: UserServiceインスタンス

    Returns:
        チャットセッションステータス
    """
    result = await user_service.apply(encrypted_position_id)
    if result:
        return {
            "session_status": result,
        }
    else:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={},
        )


@router.websocket("/chat")
@inject
async def chat(
    websocket: WebSocket,
    session_id: Optional[str] = None,
    chat_service: ChatService = Depends(Provide[Container.chat_service]),
):
    """
    会話

    Args:
        websocket: WebSocketオブジェクト
        session_id: セッションID
        chat_service: ChatServiceインスタンス

    Returns:
        None
    """
    await websocket.accept()

    try:
        if session_id is None:
            session_id = str(uuid.uuid4())
        set_session_id(session_id)
        logger.debug("Session ID: %s", session_id)

        await handle_chat_session(websocket, chat_service)
    except WebSocketDisconnect as e:
        logger.debug("Websocket切断: %s", e, exc_info=True)
    except Exception as e:
        logger.exception("予期しないエラー: %s", e)
    finally:
        clear_session_id()


async def handle_chat_session(
    websocket: WebSocket,
    chat_service: ChatService,
):
    """
    会話初期化

    Args:
        websocket: WebSocketオブジェクト
        chat_service: ChatServiceインスタンス

    Returns:
        None
    """
    # TODO: どのステータスでも会話可能。（面談応募／登録中はメインチャット不可）
    session_status, is_new_session, last_message_id = await chat_service.init_session("openai/gpt-5-mini")
    # session_status = await chat_service.init_session("bedrock/anthropic.claude-3-5-sonnet-20240620-1:0")
    if session_status == ChatSessionStatus.ERROR:
        # 初期化失敗した場合、エラー
        # TODO: フロント側のエラー処理
        await send_error_response(websocket)
    else:

        # セッションステータスが会話中または
        if session_status in [
            ChatSessionStatus.CHATTING,
            ChatSessionStatus.APPLIED,
            ChatSessionStatus.REGISTERED,
        ]:
            # 最初のメッセージを送る
            # 最初のメッセージは、2つ存在する
            # 1. 新規の会話だと、「会話開始」というdeveloperロールのメッセージ
            # 2. 既存の会話だと、「会話再開」というdeveloperロールのメッセージ
            chat_request = ChatRequestModel(
                request_type=ChatRequestType.START if is_new_session else ChatRequestType.RESTART_CHAT,
                message="会話開始" if is_new_session else "会話再開",
                current_message_id=last_message_id if not is_new_session else None,
                current_page=PageName.CHAT,
                prev_page=None,
                position_id=None
            )
            logger.debug(f"chat_request = {chat_request}")

        # Send initial session message (start or restart) to the user
        async for chunk in chat_service.chat(chat_request):
            await websocket.send_text(chunk.model_dump_json())

        # Start processing regular messages
        await process_chat_messages(websocket, chat_service)


async def send_error_response(
    websocket: WebSocket,
):
    """
    エラーレスポンス

    Args:
        websocket: WebSocketオブジェクト

    Returns:
        None
    """
    error_response = ChatStreamResponse().create_error_response("セッション初期化失敗")
    await websocket.send_text(error_response.model_dump_json())


async def process_chat_messages(
    websocket: WebSocket,
    chat_service: ChatService,
):
    """
    会話ループ

    Args:
        websocket: WebSocketオブジェクト
        chat_service: ChatServiceインスタンス

    Returns:
        None
    """
    aica_env = os.getenv("AICA_ENV")
    with trace("AICA workflow", trace_id=f"trace_{get_session_id()}", metadata={
        "AICA_ENV": aica_env if aica_env else "local",
    }):
        while True:
            if os.getenv("AICA_AGENT_MAINTENANCE_MODE") == "1":
                logger.info("メンテナンス中につきwebsocket切断")
                error_response = ChatStreamResponse().create_error_response(
                    MAINTENANCE_MESSAGE,
                    is_maintenance=True,
                )
                await websocket.send_text(error_response.model_dump_json())
                await websocket.close()
                return

            message = await websocket.receive_text()
            try:
                raw_input = json.loads(message)
                chat_request = ChatRequestModel.model_validate(raw_input)
            except json.JSONDecodeError as e:
                logger.exception("Invalid JSON: %s", e, exc_info=True)
                invalid_response = ChatStreamResponse().create_error_response(
                    "入力内容が正しくありません。",
                )
                await websocket.send_text(invalid_response.model_dump_json())
            except ValidationError as e:
                logger.exception(f"不正なリクエスト: {e}, {message}", exc_info=True)
                invalid_response = ChatStreamResponse().create_error_response(
                    "入力内容が正しくありません。",
                )
                await websocket.send_text(invalid_response.model_dump_json())
            else:
                if chat_request.request_type == ChatRequestType.LOAD_PREVIOUS_MESSAGE:
                    async for chunk in chat_service.load_previous_chat_histories(
                        chat_request
                    ):
                        await websocket.send_text(chunk.model_dump_json())
                elif chat_request.request_type == ChatRequestType.SUMMARIZE_POSITION:
                    # ポジション詳細チャットまとめ
                    session_status = await chat_service.summarize_position_detail_chat()
                    chat_response = ChatStreamResponse(
                        request_type=chat_request.request_type,
                        position_id=chat_request.position_id,
                    ).create_end_response(
                        session_status,
                    )
                    await websocket.send_text(chat_response.model_dump_json())
                else:
                    if chat_request.message:
                        async for chunk in chat_service.chat(chat_request):
                            await websocket.send_text(chunk.model_dump_json())
