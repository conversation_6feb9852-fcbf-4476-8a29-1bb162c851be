from enum import StrEnum
from typing import Any, <PERSON><PERSON>
from copy import deepcopy
from agents import Agent, ModelSettings, RunContextWrapper
from dependency_injector import resources
import logging

from agents.mcp import MCPServerStreamableHttp
from agents.mcp.util import MCPUtil
from openai import AsyncOpenAI
from openai.types.shared import Reasoning

from repositories.agent_repo import AgentRepository
from domain.entities.chat_history import ChatHistory
from utils.const import LOGGER_PREFIX
from utils.enum import LLMMessageRole

POSITION_DETAIL_INQUIRY_SUMMARY_PROMPT = """#あなたは今回の求人でユーザー会話した内容の要約してください。
要約に必要な情報:
何についてユーザーから質問されたか
回答できていた場合、その回答の求人情報の該当箇所の抜粋
事実のみを簡潔に記述。勝手な創作は推定は排除
#デバッグ用に下記のパターンに合わせた文章を出力してください
*会話の要約*には、以下を含めるようにしましょう
{{ユーザーが気にしていた情報と、それについてあなたが答えた企業情報}}
{{ユーザーが気にしていたが、あなたが答えられなかった企業情報}}
*パターンA*: *応募に誘導できる条件*に該当し、応募してみたい旨のメッセージを受け取っている場合
例:「求人情報エージェントからの引き継ぎ: ユーザーは{{企業名}}に興味を持っています。応募の案内をお願いします。会話の要約は下記です。
{{*会話の要約*}}」
*パターンB*: *応募に誘導できる条件*に該当し、特に何も言われていない場合
例:「求人情報エージェントからの引き継ぎ: ユーザーは{{企業名}}について、求人情報エージェントと下記の会話を行いましたが、応募の案内を行う必要はありません。
{{*会話の要約*}}」
*パターンC*: *応募に誘導できる条件*に該当していない場合
「求人情報エージェントからの引き継ぎ: ユーザーは{{企業名}}の求人情報を閲覧しました。この企業に対しての積極的な興味は観測できませんでした。」"""


class NotSupportedModelName(Exception):
    """
    定義されていないモデル
    """

    def __init__(self, message):
        super().__init__(message)


class AgentName(StrEnum):
    """
    エージェント名
    テーブル[agents]のカラム[name]となります。
    動的にアクティブエージェントを切り替えるときに利用するためソースにも定義しています。
    """

    DEFAULT_AGENT = "DefaultAgent"
    CAREER_ADVISOR = "CareerAdvisor"
    POSITION_GUIDE = "PositionGuide"
    POSITION_SEARCH = "PositionSearch"
    POSITION_CHANGE_ANALYZE = "PositionChangeAnalyze"


class LLMRepository(resources.AsyncResource):
    def __init__(self) -> None:
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )

        self._mcp_server = None
        self._agents: dict[str, dict[str, tuple[Agent, bool]]] = {}
        self._openai_client = AsyncOpenAI()

    async def init(
        self,
        mcp_url,
        model_list,
        agent_repository: AgentRepository,
    ):
        """
        LLMRepository初期化
        ・MCPサーバ接続
        ・ワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            mcp_url: MCPサーバーURL
            model_list: LLMモデル一覧
            agent_repository: エージェントリポジトリ
        """
        self._mcp_server = MCPServerStreamableHttp(
            name="AICA Server",
            params={
                "url": mcp_url,
            },
            max_retry_attempts=5,
        )
        try:
            await self._mcp_server.__aenter__()

            agent_models = [
                model for model in model_list if "agent" in model["use_for"]
            ]
            if not agent_models:
                raise Exception("Agent用のモデルが定義されていません")
            await self._init_agents(agent_models, agent_repository)

            summary_models = [
                model for model in model_list if "summary" in model["use_for"]
            ]
            if not summary_models:
                raise Exception(
                    "ポジション詳細チャット要約用のモデルが定義されていません"
                )
            if len(summary_models) > 1:
                self._logger.warning(
                    "複数のポジション詳細チャット要約モデルが定義されています"
                )
            self._summary_model = summary_models[0]
            if "model" not in self._summary_model:
                raise ValueError("Summary model name is not configured")
        except Exception as e:
            self._logger.exception(f"Agent初期化失敗（MCPサーバー： {mcp_url}）: {e}")
            await self.shutdown(self)
            raise

        return self

    async def shutdown(self, _: None):
        """
        LLMRepository終了処理
        ・MCPサーバ切断
        """
        if self._mcp_server is not None:
            try:
                await self._mcp_server.__aexit__(None, None, None)
            except Exception as e:
                self._logger.warning(
                    f"MCPサーバへの接続切断失敗: {e}",
                    exc_info=True,
                )
        else:
            self._logger.info("MCPサーバがないので、接続切断は不要")

    async def _init_agents(
        self,
        model_list,
        agent_repository: AgentRepository,
    ):
        """
        src/aica_agent/config.ymlの[model_list]をもとにワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            model_list: LLMモデル一覧。src/aica_agent/config.ymlの[model_list]
            agent_repository: エージェントリポジトリ（エージェント設定；デフォルトエージェントや紐づいたツール名など）
        """
        tools = await MCPUtil.get_all_function_tools(
            [self._mcp_server], True, RunContextWrapper(context=None), None
        )
        tool_names = [tool.name for tool in tools]
        self._logger.debug(f"MCP Tools: {tool_names}")
        agents = agent_repository.get_agents()
        for model in model_list:
            model_name = model["model"]
            raw_settings = deepcopy(model["model_settings"])
            reasoning = raw_settings.get("reasoning")
            if isinstance(reasoning, dict):
                raw_settings["reasoning"] = Reasoning.model_validate(reasoning)
            model_settings = ModelSettings(
                **raw_settings,
            )
            react_agents = {}
            for agent in agents:
                self._logger.debug(
                    f"Agent {agent.name}のシステムプロンプト: {agent.prompt}"
                )

                agent_tool_names = [agent_tool.tool.name for agent_tool in agent.tools]
                non_existent_tools = [
                    tool_name
                    for tool_name in agent_tool_names
                    if tool_name not in tool_names
                ]
                if non_existent_tools:
                    # DB定義ミスった場合、起動エラー
                    self._logger.error(
                        f"Agent {agent.name}のツール{non_existent_tools}がDBに存在しない。"
                    )
                    raise Exception(
                        f"Agent {agent.name}のツール{non_existent_tools}がDBに存在しない。"
                    )
                agent_tools = [tool for tool in tools if tool.name in agent_tool_names]
                react_agent = Agent(
                    model=model_name,
                    model_settings=model_settings,
                    name=agent.name,
                    instructions=agent.prompt,
                    tools=agent_tools,
                    mcp_servers=[self._mcp_server],
                )
                stop_at_tool_names = [
                    tool.tool_name for tool in agent.tools if tool.return_direct
                ]
                if stop_at_tool_names:
                    react_agent.tool_use_behavior = {
                        "stop_at_tool_names": stop_at_tool_names,
                    }

                react_agents[agent.name] = (
                    react_agent,
                    agent.next_agents,
                    agent.default_agent,
                )

            for _, (agent, next_agents, _) in react_agents.items():
                if next_agents:
                    agent.handoffs = [
                        react_agents[next_agent.dest_agent.name][0]
                        for next_agent in next_agents
                    ]

            self._agents[model_name] = {
                agent_name: (agent, default_agent)
                for agent_name, (agent, _, default_agent) in react_agents.items()
            }

    def clone_agents(self, model_name: str) -> dict[str, Tuple[Agent, bool]]:
        """
        エージェント群をクローンする。

        Args:
            model_name: LLMモデルネーム

        Returns:
            エージェント群
        """
        if model_name not in self._agents:
            self._logger.exception(f"Unsupported model name: {model_name}")
            raise NotSupportedModelName(f"Unsupported model name: {model_name}")

        # https://miidas-dev.slack.com/archives/C08CPHXCZ08/p1750994467102159
        # 性能面（あくまで推測、負荷テストで確認する必要があります）やThread-safeから考えると、
        # グローバル１つのAgentではなく、セッションごとに各自のAgentを持つ
        cloned_agents = {
            k: (v[0].clone(), v[1]) for k, v in self._agents[model_name].items()
        }
        return cloned_agents

    async def summarize_position_detail_chat(
        self,
        chat_histories: list[ChatHistory],
    ) -> str | None:
        summary_inputs = self._build_summary_input_items(chat_histories)
        if not summary_inputs:
            return None

        summary_inputs.append(
            {
                "type": "message",
                "role": LLMMessageRole.DEVELOPER.value,
                "content": [
                    {
                        "type": "input_text",
                        "text": POSITION_DETAIL_INQUIRY_SUMMARY_PROMPT,
                    }
                ],
            }
        )

        model_name = self._summary_model["model"]
        model_settings = self._summary_model.get("model_settings", {})

        try:
            # backoffを使わない。この処理は裏で走ってて、その後のユーザーメッセージ処理をブロックしてます。
            # なので、もし失敗したら、無視
            response = await self._openai_client.responses.create(
                model=model_name,
                input=summary_inputs,
                **model_settings,
            )
            return response.output_text
        except Exception as exc:
            self._logger.exception(
                "Failed to summarize position detail chat: %s",
                exc,
            )
            return None

    def _build_summary_input_items(
        self,
        histories: list[ChatHistory],
    ) -> list[dict[str, Any]]:
        summary_inputs: list[dict[str, Any]] = []
        for history in histories:
            role = history.role
            if role in (
                LLMMessageRole.USER.value,
                LLMMessageRole.DEVELOPER.value,
            ):
                message = self._build_text_message(
                    role,
                    history.content,
                    output=False,
                )
                if message:
                    summary_inputs.append(message)
            elif role == LLMMessageRole.ASSISTANT.value:
                message = self._build_text_message(
                    role,
                    history.content,
                    output=True,
                )
                if message:
                    summary_inputs.append(message)
        return summary_inputs

    def _build_text_message(
        self,
        role: str,
        content: str,
        output: bool,
    ) -> dict[str, Any]:
        """
        要約入力用のメッセージ辞書を構築する。

        Args:
            role (str): メッセージ送信者の役割（例：'user'、'developer'、'assistant'）
            content (str): メッセージのテキスト内容
            output (bool): Trueの場合、メッセージタイプは'output_text'。Falseの場合は'input_text'

        Returns:
            dict[str, Any]: メッセージを表す辞書。このメソッドは決してNoneを返しません。
        """
        return {
            "type": "message",
            "role": role,
            "content": [
                {
                    "type": "output_text" if output else "input_text",
                    "text": content,
                }
            ],
        }
