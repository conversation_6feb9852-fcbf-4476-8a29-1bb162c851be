import asyncio
import logging
import random
import aiohttp
import json
import datetime
import uuid
from typing import Any
from pathlib import Path

from client.aica_client import ChatResponseType, AICAClient, PageName
from utils.const import LOGGER_PREFIX, POSITION_ADVISOR_CHAT_END_FLAG


class E2EClient:
    def __init__(
        self,
        ws_url: str,
        api_url: str,
        model: Any,
        system_prompt: str,
        max_turns: int,
        client_id: str,
        model_name: str,
        debug_mode: bool = False,
    ):
        """
        初期化

        Args:
            ws_url (str): キャリアアドバイザーのWebSocket URL
            api_url (str): キャリアアドバイザーのAPI URL
            model (Any): 求職者LLM model
            system_prompt (str): 求職者システムプロンプト
            max_turns (int): 最大会話数
            client_id (str): キャリアアドバイザークライアントID
            model_name (str): ログ出力用モデル名
            debug_mode (bool): デバッグモード
        """
        self.ws_url = ws_url
        self.api_url = api_url
        self.model = model
        self.system_prompt = system_prompt
        self.max_turns = max_turns
        self.client_id = client_id
        self.model_name = model_name
        self.debug_mode = debug_mode

        self.client = AICAClient(ws_url, model, system_prompt, client_id)
        self.conversation_stats: list[dict[str, float]] = []
        self.logger = self._setup_logger()
        self.config: dict[str, Any] = {}
        self.session_id: str = ""
        self.turns_remaining = max_turns

    def _setup_logger(self) -> logging.Logger:
        """
        ロガーを設定する

        Returns:
            logging.Logger: ロガー
        """
        logs_dir = Path("/tmp/e2e_client")
        logs_dir.mkdir(exist_ok=True)

        now = datetime.datetime.now()
        timestamp = now.strftime("%Y%m%d%H%M%S") + f"{now.microsecond:03d}"[:3]
        unique_id = uuid.uuid4().hex
        log_filename = f"{self.client_id}_{self.model_name}_{timestamp}_{unique_id}.log"
        logger = logging.getLogger(
            f"{LOGGER_PREFIX}.client.{self.client_id}_{self.model_name}_{timestamp}_{unique_id}"
        )

        file_handler = logging.FileHandler(logs_dir / log_filename)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(
            f"[%(asctime)s] [%(levelname)s] [{self.client_id} ({self.model_name})]: %(message)s"
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.setLevel(logging.INFO)
        logger.propagate = False

        return logger

    def _log_error(self, message: str, e: Exception = None) -> None:
        """
        エラーログ出力する

        Args:
            message (str): エラーメッセージ
            e (Exception, optional): Exceptionオブジェクト

        Returns:
            None
        """
        if e:
            self.logger.exception(f"{message}: {e}")
        else:
            self.logger.error(message)

        print(f"{self.client_id} ({self.model_name}) {message}: {e}")

    def _log_info(self, message: str, console: bool = False) -> None:
        """
        ログ出力する

        Args:
            message (str): メッセージ
            console (bool): コンソール出力する

        Returns:
            None
        """
        self.logger.info(message)
        if self.debug_mode or console:
            print(f"{self.client_id} ({self.model_name}) {message}")

    async def _process_position_search_result(
        self, search_result: dict[str, Any]
    ) -> list[str]:
        """
        ポジション検索結果処理、おすすめ取得

        Args:
            search_result (dict[str, Any]): ポジション検索結果

        Returns:
            list[str]: おすすめを含めポジションIDリスト
        """
        position_uuids = [position["ID"] for position in search_result["Positions"]]
        search_key = search_result["SearchKey"]
        recommendations = search_result["Recommendations"]

        async with aiohttp.ClientSession() as session:
            tasks = []
            for recommendation in recommendations:
                url = f"{self.api_url}/positions/recommendations/{self.session_id}/{search_key}/{recommendation['Theme']}"
                tasks.append(session.get(url))

            responses = await asyncio.gather(*tasks)
            for i, response in enumerate(responses):
                recommendation = recommendations[i]
                if response.status == 200:
                    data = await response.json()
                    recommended_positions = data.get("Positions", [])
                    self._log_info(
                        f"おすすめテーマ '{recommendation}' レスポンス: {len(recommended_positions)} 件"
                    )
                    position_uuids.extend(
                        [position["ID"] for position in recommended_positions]
                    )
                else:
                    self._log_info(
                        f"おすすめテーマ '{recommendation}' エラー: {response.status}"
                    )

        return position_uuids

    async def _get_position_detail(self, position_id: str) -> tuple[int, str]:
        """
        ポジション詳細取得する

        Args:
            position_id (str): ポジションID

        Returns:
            tuple[int, str]: (
                status_code: HTTPステータスコード
                message: メッセージ
            )
        """
        urls = [
            f"{self.api_url}/positions/{self.session_id}/{position_id}",
            f"{self.api_url}/companies/{self.session_id}/{position_id}",
            f"{self.api_url}/businesses/{self.session_id}/{position_id}",
        ]

        async with aiohttp.ClientSession() as session:
            tasks = [session.get(url) for url in urls]
            responses = await asyncio.gather(*tasks)

            position_detail = None
            company_detail = None
            business_detail = None

            for i, response in enumerate(responses):
                if response.status == 200:
                    data = await response.json()
                    if i == 0:
                        position_detail = data
                    elif i == 1:
                        company_detail = data
                    elif i == 2:
                        business_detail = data

            if not position_detail:
                return (
                    404,
                    f"求人情報を取得できませんでした。session_id: {self.session_id}, position_id: {position_id}",
                )

            combined_info = f"求人情報: {position_detail}"
            if company_detail:
                combined_info += f"\n\n企業情報: {company_detail}"
            if business_detail:
                combined_info += f"\n\n事業情報: {business_detail}"

            return (
                200,
                f"求人情報を取得しました。下記となります。この求人に対して、質問してください。\n\n{combined_info}",
            )

    async def run_main_chat(self) -> None:
        """
        メインチャートループ

        Returns:
            None
        """
        while self.debug_mode or self.turns_remaining > 0 or self.max_turns == 0:
            if self.debug_mode:
                input("ENTER for continue, Ctrl+C to exit")

            agent_invoke_time, response_message = (
                await self.client.send_message_to_job_seeker_agent(
                    self.config, self.client.last_message
                )
            )
            self._log_info(f"求職LLMメッセージ:\n{response_message}")

            await self.client.send_structured_message_to_aica_agent(
                response_message, prev_page=PageName.CHAT, current_page=PageName.CHAT
            )

            _, response_type, full_message, first_msg_time, total_time = (
                await self.client.receive_streaming_response_from_aica_agent()
            )

            self.conversation_stats.append(
                {
                    "first_message_time": first_msg_time,
                    "total_response_time": total_time,
                    "agent_invoke_time": agent_invoke_time,
                }
            )

            match response_type:
                case ChatResponseType.MESSAGE:
                    self._log_info(
                        f"キャリアアドバイザーからのレスポンス:\n{full_message}"
                    )
                    self.client.last_message = full_message
                case ChatResponseType.POSITION_SEARCH_RESULT:
                    return await self.handle_position_search(full_message)
                case _:
                    self._log_error(f"エラー: {response_type} : {full_message}")
                    break

            if self.max_turns > 0:
                self.turns_remaining -= 1
                self._log_info(f"メインチャット残りのターン数: {self.turns_remaining}")

        self._log_info(f"max_turns ({self.max_turns}) に達したので、終了します。")

    async def run_position_detail_chat(self, position_uuid: str) -> None:
        """
        ポジション詳細チャットループ

        Args:
            position_uuid (str): ポジションID

        Returns:
            None
        """
        turns_remaining = (
            random.randint(self.max_turns // 3, self.max_turns)
            if self.max_turns > 0
            else random.randint(3, 5)
        )

        prev_page = PageName.CHAT
        current_page = PageName.POSITION_DETAIL
        while turns_remaining > 0:
            if self.debug_mode:
                input("ENTER for continue, Ctrl+C to exit")

            agent_invoke_time, response_message = (
                await self.client.send_message_to_job_seeker_agent(
                    self.config, self.client.last_message
                )
            )
            self._log_info(f"求職LLMメッセージ:\n{response_message}")

            await self.client.send_structured_message_to_aica_agent(
                response_message,
                prev_page=prev_page,
                current_page=current_page,
                position_id=position_uuid,
            )

            _, response_type, full_message, first_msg_time, total_time = (
                await self.client.receive_streaming_response_from_aica_agent()
            )

            self.conversation_stats.append(
                {
                    "first_message_time": first_msg_time,
                    "total_response_time": total_time,
                    "agent_invoke_time": agent_invoke_time,
                }
            )

            if response_type == ChatResponseType.MESSAGE:
                self._log_info(f"キャリアアドバイザーからのレスポンス:\n{full_message}")
                self.client.last_message = full_message
            else:
                self._log_error(f"エラー: {response_type} : {full_message}")
                break

            turns_remaining -= 1
            self._log_info(f"ポジション詳細チャット残りのターン数: {turns_remaining}")
            prev_page = PageName.POSITION_DETAIL

        await self.client.send_structured_message_to_aica_agent(
            POSITION_ADVISOR_CHAT_END_FLAG,
            prev_page=PageName.POSITION_DETAIL,
            current_page=PageName.CHAT,
        )
        await self.client.receive_streaming_response_from_aica_agent()
        self.client.last_message = (
            "求人詳細をご覧いただき、ありがとうございます。興味がありますでしょうか？"
        )

    async def handle_position_search(self, search_result_message: str) -> None:
        """
        ポジション詳細チャットにに入る前準備

        Args:
            search_result_message (str): ポジション検索結果

        Returns:
            None
        """
        search_result = json.loads(search_result_message)
        position_uuids = await self._process_position_search_result(search_result)

        position_uuid: str = random.choice(position_uuids)
        status, position_detail_message = await self._get_position_detail(position_uuid)

        if status == 200:
            self.client.last_message = position_detail_message
            await self.run_position_detail_chat(position_uuid)
            await self.run_main_chat()
        else:
            self.client.last_message = "申し訳ございませんが、求人詳細を取得できませんでした。他にご質問はありますか？"
            await self.run_main_chat()

    async def run(self) -> dict[str, Any]:
        """
        E2Eテスト実行

        Returns:
            dict[str, Any]: テスト結果データ
        """
        try:
            await self.client.connect()
            self._log_info("接続済み")

            await self.client.send_structured_message_to_aica_agent(
                "こんにちは", prev_page=PageName.NONE, current_page=PageName.CHAT
            )

            self.session_id, response_type, full_message, first_msg_time, total_time = (
                await self.client.receive_streaming_response_from_aica_agent()
            )

            if response_type != ChatResponseType.MESSAGE:
                self._log_error(f"サポートしないmessage_type: {response_type}")
                return {}

            self.config = {"configurable": {"thread_id": self.session_id}}
            self._log_info(f"session_id: {self.session_id}", True)
            self._log_info(f"キャリアアドバイザーからのレスポンス:\n{full_message}")

            self.client.last_message = full_message
            self.conversation_stats.append(
                {
                    "first_message_time": first_msg_time,
                    "total_response_time": total_time,
                    "agent_invoke_time": 0,
                }
            )

            await self.run_main_chat()

        except KeyboardInterrupt:
            self._log_info("接続切断中...")
        except Exception as e:
            self._log_error("予期しないエラー", e)
        finally:
            await self.client.close()
            self._log_info("接続切断済")

        return {
            "persona": self.client_id,
            "model": self.model_name,
            "turns": len(self.conversation_stats),
            "stats": self.conversation_stats,
        }
