from contextlib import Abstract<PERSON>ontextManager
from typing import Callable, <PERSON><PERSON>, Optional
from sqlalchemy import select, func
from sqlalchemy.orm import Session, joinedload, aliased

from domain.entities.chat_history import ChatHistory
from domain.entities.chat_session import ChatSession, ChatSessionStatus
from domain.entities.user_profile import UserProfile
from utils.logging import get_session_id


class ChatRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    def init_chat_session(self) -> Tuple[Optional[ChatSession], bool]:
        """
        会話履歴とユーザー検索条件取得

        Args:
            None

        Returns:
            会話履歴 or None
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            HistoryAlias = aliased(ChatHistory)
            ProfileAlias = aliased(UserProfile)
            stmt = (
                select(ChatSession)
                .options(
                    joinedload(
                        ChatSession.histories.of_type(HistoryAlias).and_(
                            HistoryAlias.deleted_at.is_(None)
                        )
                    ),
                    joinedload(
                        ChatSession.user_profile.of_type(ProfileAlias).and_(
                            ProfileAlias.deleted_at.is_(None)
                        )
                    ),
                )
                .filter(
                    ChatSession.session_id == session_id,
                    ChatSession.deleted_at.is_(None),
                )
                .order_by(HistoryAlias.id)
            )
            chat_session = session.scalars(stmt).unique().first()
            if chat_session:
                return (chat_session, True)
            else:
                # 指定session_idのセッションは期限切れで削除されたかを確認します。
                count_stmt = (
                    select(func.count())
                    .select_from(ChatSession)
                    .where(
                        ChatSession.session_id == session_id,
                        ChatSession.deleted_at.isnot(None),
                    )
                )
                count = session.execute(count_stmt).scalar_one()
                return (None, count > 0)

    def create_chat_session(
        self,
        session_status: ChatSessionStatus,
    ):
        """
        会話セッション作成する。

        Args:
            session_status: セッション作成時のセッションステータス
        """
        with self._session_factory() as session:
            chat_session = ChatSession(
                session_id=get_session_id(),
                status=session_status,
            )
            session.add(chat_session)
            session.commit()

    def add_chat_histories(
        self,
        chat_histories: list[ChatHistory],
    ):
        """
        複数会話を追加する。

        Args:
            chat_histories: 会話履歴
        """
        with self._session_factory() as session:
            # 同じChatHistoryインスタンスが他の場所で再利用されたり、以前に別のセッションに
            # 添付されていた場合のSQLAlchemyセッションバインディング問題を避けるためにクローンが必要。
            for chat_history in chat_histories:
                session.add(self.clone_chat_history(chat_history))
            session.commit()

    @staticmethod
    def clone_chat_history(
        chat_history: ChatHistory,
    ) -> ChatHistory:
        """
        SQLAlchemyのセッションバインディング問題を防ぐためにChatHistoryオブジェクトをクローンする。
        
        SQLAlchemyセッションにオブジェクトを追加すると、そのオブジェクトはセッションにバインドされる。
        セッションが非アクティブになった後にオブジェクトを変更すると、エラーが発生する可能性がある。
        クローンを作成することで、安全に使用できる新しいインスタンスを作成する。
        
        Args:
            chat_history: クローンする元のChatHistoryオブジェクト
            
        Returns:
            同じデータを持つ新しいChatHistoryインスタンス
        """
        return ChatHistory(
            session_id=chat_history.session_id,
            position_id=chat_history.position_id,
            active_agent=chat_history.active_agent,
            message_id=chat_history.message_id,
            role=chat_history.role,
            content=chat_history.content,
            tool_call_id=chat_history.tool_call_id,
            tool_name=chat_history.tool_name,
            tool_input=chat_history.tool_input,
        )

    def add_chat_history(
        self,
        chat_history: ChatHistory,
    ) -> None:
        """
        １つの会話を追加する。

        Args:
            chat_history: 会話履歴
        """
        with self._session_factory() as session:
            session.add(self.clone_chat_history(chat_history))
            session.commit()

    def session_status(
        self,
    ) -> ChatSessionStatus | None:
        """
        該当セッションのステータス

        Args:
            None

        Returns:
            セッション存在する場合、status
            存在してない場合、None
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            stmt = select(ChatSession).filter(
                ChatSession.session_id == session_id,
                ChatSession.deleted_at.is_(None),
            )
            chat_session = session.scalars(stmt).first()
            if chat_session:
                return chat_session.status
            return None

    def update_session_status(
        self,
        session_status: ChatSessionStatus,
    ) -> ChatSessionStatus | None:
        """
        該当セッションのステータスを更新する

        Args:
            session_status: 新しいステータス

        Returns:
            セッション存在する場合、新しいステータス
            存在してない場合、None
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            stmt = select(ChatSession).filter(
                ChatSession.session_id == session_id,
                ChatSession.deleted_at.is_(None),
            )
            chat_session = session.scalars(stmt).first()
            if chat_session:
                chat_session.status = session_status
                session.commit()
                return session_status
            return None

    def get_tool_input(
        self,
        tool_call_id: str,
    ) -> dict | None:
        """
        ツールINPUT取得
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            stmt = (
                select(ChatHistory)
                .filter(
                    ChatHistory.session_id == session_id,
                    ChatHistory.tool_call_id == tool_call_id,
                    ChatHistory.deleted_at.is_(None),
                )
                .order_by(ChatHistory.id.desc())
            )
            chat_history = session.scalars(stmt).first()
            if chat_history:
                return chat_history.tool_input
            return None
