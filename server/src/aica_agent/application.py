"""
FastAPI入口
"""

from contextlib import asynccontextmanager
from logging import getLogger
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from containers import Container
import endpoints
from utils.logging import add_tracing_info, clear_tracing_info
from utils.const import API_PREFIX, LOGGER_PREFIX

container = Container()


@asynccontextmanager
async def lifespan(_: FastAPI):
    await container.init_resources()
    yield
    await container.shutdown_resources()


app = FastAPI(lifespan=lifespan)


@app.middleware("http")
async def maintenance_mode_middleware(request: Request, call_next):
    """
    メンテナンス中の場合、APIエンドポイントへのアクセスを制限するミドルウェア
    ヘルスチェックエンドポイントは除外される
    Note: WebSocketエンドポイントはHTTPミドルウェアの対象外のため自動的に除外される
    """
    import os
    from utils.const import MAINTENANCE_MESSAGE

    # ヘルスチェックエンドポイントは除外
    if request.url.path == f"{API_PREFIX}/health":
        return await call_next(request)

    # メンテナンスモードチェック
    if os.getenv("AICA_AGENT_MAINTENANCE_MODE") == "1":
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "message": MAINTENANCE_MESSAGE,
                "isMaintenance": True,
            },
        )

    return await call_next(request)


@app.middleware("http")
async def add_tracing_info_context(request: Request, call_next):
    """
    リクエスト毎にセッションIDとリクエストIDをcontextに追加する

    Args:
        request: リクエスト
        call_next: 次の処理

    Returns:
        レスポンス
    """
    add_tracing_info(request)
    try:
        response = await call_next(request)
        return response
    finally:
        clear_tracing_info()


# FIXME: フロントエンドからのみ許可すべき
# Copilotからの指摘
# Using allow_credentials=True together with allow_origins=["*"] is invalid in Starlette/FastAPI CORS middleware and will raise a ValueError at startup; additionally it is a security risk because credentialed requests require explicit trusted origins. Specify concrete origins (e.g. allow_origins=[FRONTEND_ORIGIN]) or disable credentials if wildcard origin is required.
# CORSミドルウェアを追加（HTTPミドルウェアの後に追加することで全てのレスポンスに適用される）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 全てのオリジンを許可
    allow_credentials=True,
    allow_methods=["*"],  # 全てのメソッドを許可
    allow_headers=["*"],  # 全てのヘッダーを許可
)

app.container = container
app.include_router(endpoints.router)


@app.exception_handler(RequestValidationError)
async def validation_error_handler(request: Request, exc: RequestValidationError):
    """
    リクエストバリデーションエラー処理
    現在リクエストパラメータバリデーションはプロフィール入力しかないですが、
    フロントの方は制御しているので、サーバー側のバリデーションエラーが発生しないはず
    発生する場合、Attackかいたずらとしか考えられないので、詳細エラー内容を返さない。
    フロント側もサーバー側バリデーションエラーの発生を考慮してない。

    Args:
        request: リクエスト
        exc: 例外

    Returns:
        JSONResponse: エラーレスポンス
    """
    logger = getLogger(f"{LOGGER_PREFIX}.{__name__}")
    logger.error(f"ValidationError caught: {exc} for URL: {request.url}")

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={},
    )
