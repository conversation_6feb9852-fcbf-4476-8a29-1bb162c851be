from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, String, ARRAY
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import JSONB
from database import Base
from pydantic import BaseModel, EmailStr, Field, model_validator, field_validator
import re
from datetime import datetime

from utils.enum import (
    CompanyCount,
    DriverLicence,
    EmployeeSize,
    EmploymentPost,
    EmploymentType,
    EnglishLevel,
    ExperienceYears,
    FirstLanguage,
    Gender,
    JobChangePeriod,
    LocationType,
    ManagementPeople,
    Month,
    SchoolType,
)


class UserProfile(Base):
    __tablename__ = "user_profiles"

    id = Column(Integer, primary_key=True)
    session_id: Mapped[str] = mapped_column(ForeignKey("chat_sessions.session_id"))
    user_name = Column(String, nullable=True)
    user_purpose = Column(String, nullable=True)
    interest_tendency = Column(String, nullable=True)
    job_search_motivation = Column(String, nullable=True)
    current_job_experience_years = Column(Integer, nullable=True)
    current_job_description = Column(String, nullable=True)
    job_search_filter = Column(JSONB, nullable=True)
    job_feedback_positive = Column(ARRAY(String), nullable=True)
    job_feedback_negative = Column(ARRAY(String), nullable=True)
    miidas_registration_user_data: Mapped[JSONB] = mapped_column(JSONB, nullable=True)
    deleted_at = Column(DateTime, nullable=True)


class IDNameModel(BaseModel):
    id: int = Field(..., alias="ID")
    name: str = Field(..., alias="Name")


class AddressModel(BaseModel):
    prefecture: IDNameModel
    city: IDNameModel


class BaseJSONModel(BaseModel):
    @field_validator("*", mode="before")
    @classmethod
    def convert_empty_str_to_none(cls, v):
        """Convert empty string values to None for all fields"""
        if v == "":
            return None
        return v


class BaseJSONUserProfile(BaseJSONModel):
    pass


class JSONUserProfileBasicInfo(BaseJSONUserProfile):
    """
    ユーザーの基本的なプロフィールモデル
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    gender: Gender = Field(...)  # 性別
    last_name: str = Field(..., max_length=50, alias="lastName")
    first_name: str = Field(..., max_length=50, alias="firstName")
    last_name_kana: str = Field(..., max_length=50, alias="lastNameKana")
    first_name_kana: str = Field(..., max_length=50, alias="firstNameKana")
    birth_year: int = Field(..., alias="birthYear")
    birth_month: Month = Field(..., alias="birthMonth")
    email: EmailStr = Field(..., max_length=100)
    password: str = Field(..., min_length=8, max_length=16)
    phone_no: str = Field(..., min_length=10, max_length=13, alias="phoneNo")
    prefecture: IDNameModel
    city: IDNameModel
    first_language: FirstLanguage = Field(..., alias="firstLanguage")  # 第一言語
    driver_licence: DriverLicence = Field(
        ..., alias="driverLicence"
    )  # 運転免許証の有無

    @field_validator("birth_year")
    @classmethod
    def validate_birth_year(cls, v):
        """
        誕生日の年の妥当性検証
        """
        current_year = datetime.now().year
        if v <= current_year - 100:
            raise ValueError(f"{current_year - 100}以降の年を入力してください")
        if v >= current_year - 10:
            raise ValueError(f"{current_year - 10}までの年を入力してください")
        return v

    @field_validator("password")
    @classmethod
    def validate_password(cls, v):
        """
        パスワードの妥当性検証
        英数字を含む8-16文字
        """
        if not re.match(r"^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$", v):
            raise ValueError("パスワードは英字と数字を含む8-16文字である必要があります")
        return v


# Get the current year
CURRENT_DATETIME = datetime.now()
CURRENT_YEAR = CURRENT_DATETIME.year
MINIMUM_YEAR = 1940  # 卒業年、入社年または退社年


class JSONUserProfileEducation(BaseJSONUserProfile):
    """
    ユーザーの学歴モデル。
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    school_type: SchoolType = Field(
        ...,
        alias="schoolType",
    )  # 学校種別
    graduation_year: int = Field(
        ...,
        ge=MINIMUM_YEAR,
        le=CURRENT_YEAR,
        alias="graduationYear",
    )  # 卒業年: 1940年から現在の年まで
    english_level: EnglishLevel = Field(
        ...,
        alias="englishLevel",
    )  # 英語レベル
    school_name: str | None = Field(
        None,
        max_length=200,
        alias="schoolName",
    )
    department: IDNameModel | None = Field(
        None,
        alias="department",
    )
    professional_training_college_category: IDNameModel = Field(
        ...,
        alias="professionalTrainingCollegeCategory",
    )

    @model_validator(mode="after")
    def conditional_validation(self):
        """
        条件付きバリデーション
        """
        # school_typeが専門学校、高等学校、中学校以外の場合、school_nameとdepartmentが必須
        if SchoolType.requires_department(self.school_type):
            if not self.school_name or self.school_name.strip() == "":
                raise ValueError("学校名は必須です")
            if self.department is None or self.department.id is None:
                raise ValueError("学部・学科は必須です")

        # school_typeが専門学校の場合、professional_training_college_categoryが必須
        if self.school_type == SchoolType.VOCATIONAL_SCHOOL:
            if (
                self.professional_training_college_category is None
                or self.professional_training_college_category.id is None
            ):
                raise ValueError("専門学校カテゴリは必須です")

        return self


class JSONUserProfileCarrer(BaseJSONUserProfile):
    """
    ユーザーの経歴モデル。
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    exp_company_num: CompanyCount = Field(
        ...,
        alias="expCompanyNum",
    )  # 経験社数
    management_exp_term: ExperienceYears = Field(
        ...,
        alias="managementExpTerm",
    )  # 今までのマネジメント経験年数
    management_people_num: ManagementPeople | None = Field(
        None,
        alias="managementPeopleNum",
    )  # 今までのマネジメント経験人数
    company_name: str | None = Field(
        None,
        max_length=255,
        alias="companyName",
    )  # 企業名
    industry_small: IDNameModel | None = Field(
        None,
        alias="industrySmall",
    )  # 業種
    employee_num: EmployeeSize | None = Field(
        None,
        alias="employeeNum",
    )  # 企業規模
    employment_type: EmploymentType | None = Field(
        None,
        alias="employmentType",
    )  # 直近企業の雇用形態
    employment_post: EmploymentPost | None = Field(
        None,
        alias="employmentPost",
    )  # 直近企業の役職
    job_type_small: IDNameModel | None = Field(
        None,
        alias="jobTypeSmall",
    )  # 職種
    job_type_exp_term: ExperienceYears | None = Field(
        None,
        alias="jobTypeExpTerm",
    )  # 経験職種の経験年数（直近の企業のみ）
    all_career_job_type_exp_term: ExperienceYears | None = Field(
        None,
        alias="allCareerJobTypeExpTerm",
    )  # 経験職種の経験年数（トータル）
    income: int | None = Field(
        None,
        ge=1,
        le=10000,
        alias="income",
    )  # 年収 最大1億
    join_year: int | None = Field(
        None,
        ge=MINIMUM_YEAR,
        le=CURRENT_YEAR,
        alias="joinYear",
    )
    join_month: Month | None = Field(
        None,
        alias="joinMonth",
    )
    retire_year: int | None = Field(
        None,
        ge=MINIMUM_YEAR,
        le=CURRENT_YEAR,
        alias="retireYear",
    )
    retire_month: Month | None = Field(
        None,
        alias="retireMonth",
    )

    @model_validator(mode="after")
    def conditional_validation(self):
        """
        条件付きバリデーション
        """
        if (
            self.management_exp_term != ExperienceYears.NONE
            and self.management_people_num is None
        ):
            raise ValueError("今までのマネジメント経験人数は必須です")

        # 経験者数が1社以上であればその他の項目は必須になる
        # マスタの1=ゼロ社
        # マスタの2=1社なので、ここでは1より大きいで比較する
        if self.exp_company_num != CompanyCount.ZERO:
            if self.company_name is None:
                raise ValueError("企業名は必須です")
            if self.income is None:
                raise ValueError("年収は必須です")
            if self.industry_small.id is None:
                raise ValueError("業種は必須です")
            elif self.industry_small.id < 101010 or self.industry_small.id > 441010:
                raise ValueError("業種は無効です")
            if self.employee_num is None:
                raise ValueError("企業規模は必須です")
            if self.job_type_small.id is None:
                raise ValueError("職種は必須です")
            elif self.job_type_small.id < 101010 or self.job_type_small.id > 451126:
                raise ValueError("職種は無効です")
            if self.join_year is None or self.join_month is None:
                raise ValueError("入社年月は必須です")
            if self.employment_type is None:
                raise ValueError("雇用形態は必須です")
            if self.employment_post is None:
                raise ValueError("役職は必須です")
            if self.job_type_exp_term is None:
                raise ValueError("直近企業での職種経験年数は必須です")
            if self.all_career_job_type_exp_term is None:
                raise ValueError("トータル職種経験年数は必須です")
            if self.job_type_exp_term > self.all_career_job_type_exp_term:
                raise ValueError(
                    "直近企業での職種経験年数はトータル職種経験年数を超えてはいけません"
                )

            # 退社年月はNoneであれば在籍中という意味
            if self.retire_year is not None and self.retire_month is not None:
                # 但し、退社年月は入社年月よりも未来でなければなりません
                join_date = datetime(self.join_year, self.join_month, 1)
                retire_date = datetime(self.retire_year, self.retire_month, 1)

                if retire_date <= join_date:
                    raise ValueError("退社年月は入社年月よりも過去であってはなりません")
        return self


class JSONUserProfileWill(BaseJSONUserProfile):
    """
    ユーザーの希望条件モデル。
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    will_income: int = Field(
        ...,
        ge=100,
        le=2000,
        alias="willIncome",
    )  # 希望年収: 100万円から2000万円まで,
    will_work_addresses_cities: list[AddressModel] = Field(
        ...,
        min_length=1,
        max_length=40,
        alias="willWorkAddressesCities",
    )  # 希望勤務地リスト
    will_remote_work: bool = Field(
        ...,
        alias="willRemoteWork",
    )  # 在宅勤務を希望する
    will_job_types_smalls: list[IDNameModel] = Field(
        ...,
        min_length=1,
        max_length=40,
        alias="willJobTypesSmalls",
    )  # 希望職種リスト
    will_job_change_period: JobChangePeriod = Field(
        ...,
        alias="willJobChangePeriod",
    )  # 転職希望時期
    is_rpo_agreement: bool = Field(
        ...,
        alias="isRpoAgreement",
    )  # 希望する求人マッチングサービスを利用するかどうか

    @model_validator(mode="after")
    def validate_unique_prefectures(self):
        """
        希望勤務地の都道府県数チェック
        """
        # 希望勤務地リストからUnique都道府県IDを取得
        unique_prefecture_ids = set()
        for address in self.will_work_addresses_cities:
            unique_prefecture_ids.add(address.prefecture.id)

        # 重複しない都道府県数が5以下であることを確認
        if len(unique_prefecture_ids) > 5:
            raise ValueError("希望勤務地の都道府県は最大5つの都道府県まで選択可能です")

        return self


class JSONSearchKeyword(BaseModel):
    """
    キーワード検索リクエストモデル
    """

    keyword: str = Field(..., min_length=1)

    @model_validator(mode="after")
    def validate_keyword(self):
        """
        キーワードの妥当性検証
        """
        if not self.keyword or self.keyword.strip() == "":
            raise ValueError("キーワードは必須です")

        self.keyword = self.keyword.strip()

        return self


class JSONSearchLocation(BaseJSONModel):
    """
    通勤エリア検索リクエストモデル
    """

    location_type: LocationType | None = Field(None)
    prefecture_name: str | None = Field(None)
    city_name: str | None = Field(None)

    @field_validator("location_type")
    @classmethod
    def validate_location_type(cls, v):
        """
        location_typeの妥当性検証
        フルリモートは通勤エリア検索では使用不可
        """
        if v == LocationType.FULL_REMOTE:
            raise ValueError("通勤エリア検索ではフルリモートは選択できません")
        return v

    @model_validator(mode="after")
    def validate_keyword(self):
        if not self.city_name or self.city_name.strip() == "":
            raise ValueError("市区町村名は必須です")

        self.prefecture_name = (
            self.prefecture_name.strip() if self.prefecture_name else None
        )
        self.city_name = self.city_name.strip() if self.city_name else None

        return self
