import copy
from datetime import datetime
import logging
from contextlib import AbstractContextManager
from typing import Any, Callable

from domain.entities.user_profile import UserProfile
from repositories.api_repo import AICAAPIRepository
from utils.const import LOGGER_PREFIX
from utils.crypt import create_secret_key, decrypt, encrypt
from utils.enum import EncryptKeyType
from utils.logging import get_session_id
from utils import sync_dict


class PositionRepository:
    """
    セッション毎にポジション関連情報のキャッシュ
    """

    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Any]],
        aica_api_repository: AICAAPIRepository,
    ):
        """
        下記のポジション情報キャッシュ初期化
        ・暗号化・復号化のキー（セッション毎異なる）
        ・ポジション検索結果
        ・ポジション詳細表示に関連する情報
            ・ポジション詳細
            ・会社詳細
            ・業界詳細
        ・ユーザー検索条件
        """
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )
        self._session_factory = session_factory
        self._aica_api_repository = aica_api_repository

        self._position_search_result_ids = sync_dict.SynchronizedDict[
            str, dict[str, list[int]]
        ]()
        self._position_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._company_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._business_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._user_preferences = sync_dict.SynchronizedDict[str, dict[str, Any]]()

    def _encrypt_id(self, real_id: str) -> str:
        """
        ポジションIDを暗号化する

        Args:
            real_id: 実際のポジションID

        Returns:
            暗号化されたポジションID
        """
        key = create_secret_key(EncryptKeyType.POSITION)
        return encrypt(key, real_id)

    def decrypt_id(self, encrypted_id: str) -> str:
        """
        ポジションIDを復号化する

        Args:
            encrypted_id: 暗号化されたポジションID

        Returns:
            復号化されたポジションID
        """
        key = create_secret_key(EncryptKeyType.POSITION)
        return decrypt(key, encrypted_id)

    def save_search_result_position_ids(
        self,
        search_key: str,
        position_ids: list[int],
    ) -> int:
        """
        ポジション検索結果IDをキャッシュする

        Args:
            search_key: 検索キー
            position_ids: 検索結果のポジションIDリスト

        Returns:
            ポジション検索結果数
        """
        if not position_ids:
            return 0

        unique_position_ids = list(dict.fromkeys(position_ids))

        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        session_data[search_key] = unique_position_ids

        return len(unique_position_ids)

    def get_search_result_position_ids(
        self,
        search_key: str,
        offset: int,
        limit: int,
    ) -> list[int]:
        """
        ポジション検索結果IDを取得

        Args:
            search_key: 検索キー

        Returns:
            ポジション検索結果IDリスト
        """
        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        return session_data.get(search_key, [])[offset : offset + limit]

    def get_search_result_count(self, search_key: str) -> int:
        """
        ポジション検索結果数を取得

        Args:
            search_key: 検索キー

        Returns:
            ポジション検索結果数
        """
        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        return len(session_data.get(search_key, []))

    def remove_search_result_positions_ids(
        self,
        search_key: str,
        position_ids: list[int],
    ) -> int:
        """
        ポジション検索結果IDを削除

        Args:
            search_key: 検索キー
            position_ids: 削除するポジションIDリスト
        """
        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        if search_key in session_data:
            session_data[search_key] = [
                pid for pid in session_data[search_key] if pid not in position_ids
            ]

        return len(session_data.get(search_key, []))

    def process_and_cache_positions(
        self,
        positions: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """
        ポジション検索結果をキャッシュする

        Args:
            positions: 検索結果

        Returns:
            処理後のポジションリスト
        """
        if not positions:
            return []

        for position in positions:
            real_id = str(position["ID"])
            try:
                position["ID"] = self._encrypt_id(real_id)
            except Exception:
                self._logger.exception("ポジションID暗号化失敗 %s", real_id)
                return []

        return positions

    def save_position_detail(
        self,
        position_id: str,
        position_detail: dict,
    ):
        """
        ポジション詳細APIレスポンスをキャッシュする

        Args:
            position_id: ポジションID
            position_detail: ポジション詳細APIレスポンス
        """
        position_details = self._position_details.setdefault(get_session_id(), {})
        position_details[position_id] = position_detail

    async def get_position_detail(
        self,
        position_id: str,
        encrypted: bool = True,
    ) -> dict | None:
        """
        ポジション詳細はそんなに頻繁に更新されるわけではないし
        同じ会話中でしたらそんなに時間が経ってないはず
        なので、キャッシュにこの会話で取得済みのポジションがありましたら、利用し、
        なければ、APIサーバから取得します。

        Args:
            position_id: ポジションID
            encrypted: 暗号化されたポジションIDか否か

        Returns:
            ジション詳細APIレスポンス
        """
        if encrypted:
            try:
                position_id = self.decrypt_id(position_id)
            except Exception as e:
                self._logger.exception(
                    "ポジションID復号化が失敗しました: %s",
                    position_id,
                )
                return None

        position_details = self._position_details.setdefault(get_session_id(), {})
        position_detail = position_details.get(position_id)
        if position_detail:
            return position_detail

        api_path = f"positions/detail/{position_id}"
        self._logger.debug(f"ポジション詳細APIリクエスト: {api_path}")
        _, position_detail = await self._aica_api_repository.post(api_path)

        if position_detail:
            self.save_position_detail(
                position_id,
                position_detail,
            )

        return position_detail

    def save_company_detail(
        self,
        position_id: str,
        company_detail: dict,
    ):
        """
        会社詳細APIレスポンスをキャッシュする

        Args:
            position_id: ポジションID
            company_detail: 会社詳細APIレスポンス
        """
        company_details = self._company_details.setdefault(get_session_id(), {})
        company_details[position_id] = company_detail

    async def get_company_detail(
        self,
        position_id: str,
        encrypted: bool = True,
    ) -> dict | None:
        """
        会社詳細はそんなに頻繁に更新されるわけではないし
        同じ会話中でしたらそんなに時間が経ってないはず
        なので、キャッシュにこの会話で取得済みのポジションがありましたら、利用し、
        なければ、APIサーバから取得します。

        Args:
            position_id: ポジションID
            encrypted: 暗号化されたポジションIDか否か

        Returns:
            会社詳細APIレスポンス
        """
        if encrypted:
            try:
                position_id = self.decrypt_id(position_id)
            except Exception as e:
                self._logger.exception(
                    "ポジションID復号化が失敗しました: %s",
                    position_id,
                )
                return None

        company_details = self._company_details.setdefault(get_session_id(), {})
        company_detail = company_details.get(position_id)
        if company_detail:
            return company_detail

        api_path = f"companies/detail/position_id/{position_id}"
        self._logger.debug(f"会社詳細APIリクエスト: {api_path}")
        _, company_detail = await self._aica_api_repository.get(api_path)

        if company_detail:
            self.save_company_detail(
                position_id,
                company_detail,
            )
        return company_detail

    def save_business_detail(
        self,
        position_id: str,
        business_detail: dict,
    ):
        """
        業界詳細APIレスポンスをキャッシュする

        Args:
            position_id: ポジションID
            business_detail: 業界詳細APIレスポンス
        """
        business_details = self._business_details.setdefault(get_session_id(), {})
        business_details[position_id] = business_detail

    async def get_business_detail(
        self,
        position_id: str,
        encrypted: bool = True,
    ) -> dict | None:
        """
        キャッシュから業界詳細APIレスポンスを取得

        Args:
            position_id: ポジションID
            encrypted: 暗号化されたポジションIDか否か

        Returns:
            業界詳細APIレスポンス
        """
        if encrypted:
            try:
                position_id = self.decrypt_id(position_id)
            except Exception as e:
                self._logger.exception(
                    "ポジションID復号化が失敗しました: %s",
                    position_id,
                )
                return None

        business_details = self._business_details.setdefault(get_session_id(), {})
        business_detail = business_details.get(position_id)
        if business_detail:
            return business_detail

        api_path = f"businesses/detail/position_id/{position_id}"
        self._logger.debug(f"業界詳細APIリクエスト: {api_path}")
        _, business_detail = await self._aica_api_repository.get(api_path)

        if business_detail:
            self.save_business_detail(
                position_id,
                business_detail,
            )
        return business_detail

    def save_position_recommendations(
        self,
        recommendation_themes: list[str],
    ) -> dict[str, str]:
        """
        おすすめのリンクマッピング作成

        Args:
            recommendation_themes: おすすめパスリスト

        Returns:
            レコメンドリンクマッピング
        """
        return {theme: self._encrypt_id(theme) for theme in recommendation_themes}

    def get_position_recommendation_theme(
        self,
        encrypted_theme: str,
    ) -> str | None:
        """
        おすすめのリンクマッピングからパスを取得

        Args:
            encrypted_theme: 暗号化されたおすすめテーマ

        Returns:
            おすすめテーマ
        """
        try:
            return self.decrypt_id(encrypted_theme)
        except Exception:
            return None

    def save_user_preference(
        self,
        preference: dict[str, Any],
    ):
        """
        ユーザーの検索条件をキャッシュする

        Args:
            preference: ユーザーの検索条件
        """
        self._logger.debug(f"save_user_preference: {preference}")
        session_data = self._user_preferences.setdefault(get_session_id(), {})
        session_data.update(preference)

    def get_user_preferences(
        self,
    ) -> dict | None:
        """
        キャッシュからユーザーの検索条件を取得

        Args:
            None

        Returns:
            ユーザーの検索条件
        """
        session_data = self._user_preferences.setdefault(get_session_id(), {})
        if not session_data:
            self._logger.debug("ユーザー検索条件がキャッシュにないのでDBから取得します")
            # 提案はポジション検索と同じ条件で行う想定ですが、
            # 検索後、なにかの原因でwebsocketが切断したら、セッションデータがなくなった場合
            # ユーザーのポジション検索条件がセッションから取れなくなりますので
            # 提案はポジション検索と同じ条件で検索できなくなります。
            # なので、セッションにない場合、DBから取得します。
            preference = self._load_user_preference_from_db()
            if not preference:
                return None
            session_data.update(preference)

        return copy.deepcopy(session_data) if session_data else {}

    def _load_user_preference_from_db(
        self,
    ) -> dict[str, Any] | None:
        """
        DBからユーザーの検索条件を取得
        """
        session_id = get_session_id()
        try:
            with self._session_factory() as session:
                user_profile = (
                    session.query(UserProfile)
                    .filter(
                        UserProfile.session_id == session_id,
                        UserProfile.deleted_at.is_(None),
                    )
                    .first()
                )
        except Exception:
            self._logger.exception(
                "DBからユーザー検索条件の取得に失敗しました: %s", session_id
            )
            return None

        if not user_profile:
            return None

        preference = user_profile.job_search_filter
        return preference if isinstance(preference, dict) else None

    def process_position_search_result(
        self,
        search_result: dict[str, Any] | None,
    ) -> dict[str, Any]:
        """
        ポジション検索結果処理

        Args:
            search_result: ポジション検索結果

        Returns:
            処理後のポジション検索結果
        """
        search_key = str(int(datetime.now().timestamp()))
        count = self.save_search_result_position_ids(
            search_key,
            search_result.get("AllPositionIds"),
        )

        position_summaries = self.process_and_cache_positions(
            search_result.get("Positions"),
        )

        recommendations = search_result.get("Recommendations", [])
        if recommendations:
            theme_mapping = self.save_position_recommendations(
                [rec["Theme"] for rec in recommendations],
            )
            for rec in recommendations:
                rec["Theme"] = theme_mapping.get(rec.get("Theme"))

        return {
            "SearchKey": search_key,
            "TotalPositionCount": count,
            "Positions": position_summaries,
            "Recommendations": recommendations,
        }
