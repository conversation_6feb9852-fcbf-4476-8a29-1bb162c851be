from enum import StrEnum

from pydantic import BaseModel, ConfigDict, Field

from utils.enum import PageName


class ChatRequestType(StrEnum):
    # 会話開始
    START = "start"
    # チャット
    CHAT = "chat"
    # 過去チャット再開
    RESTART_CHAT = "restart_chat"
    # 過去チャット履歴取得
    LOAD_PREVIOUS_MESSAGE = "load_previous_message"
    # ポジション詳細チャットまとめ
    SUMMARIZE_POSITION = "summarize_position"


class ChatRequestModel(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    request_type: ChatRequestType = ChatRequestType.CHAT
    current_page: PageName
    previous_page: PageName | None = None
    message: str | None = Field(
        default=None,
        strip_whitespace=True,
    )
    position_id: str | None = None
    current_message_id: str | None = None
