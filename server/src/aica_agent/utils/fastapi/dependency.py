import logging
import re
from dependency_injector.wiring import Provide, inject
from fastapi import Depends, HTTPException, Request

from containers import Container
from domain.entities.chat_session import ChatSessionStatus
from repositories.chat_repo import ChatRepository
from utils.const import LOGGER_PREFIX
from utils.crypt import create_secret_key, decrypt
from utils.enum import ComponentName, EncryptKeyType

logger = logging.getLogger(f"{LOGGER_PREFIX}.{__name__}")

@inject
async def source_component_analysis(
    request: Request,
):
    """遷移元コンポーネントの情報を分析ログへ出力する依存関数"""

    source_component = request.headers.get("X-SOURCE-COMPONENT")
    if source_component is None or source_component == ComponentName.POSITION:
        # 遷移元情報がない場合（リロード時）、および遷移元がポジション一覧の場合はそのままログ出力
        logger.info(
            "analyze_click_position_detail",
            extra={"source_component": source_component},
        )
        return

    # 遷移元がおすすめ一覧の場合は「recommendation_[暗号化済みテーマ名]」の形式
    # アンダースコアで分割しテーマを取得
    parts = source_component.split("_", 1)
    if len(parts) == 2 and parts[0] == ComponentName.RECOMMENDATION:
        source_component = parts[0]
        encrypted_theme = parts[1]

        try:
            key = create_secret_key(EncryptKeyType.POSITION)
            decoded_theme = decrypt(key, encrypted_theme)
            sanitized_theme = re.sub(r"[^a-zA-Z0-9_.-]", "", decoded_theme)
            logger.info(
                "analyze_click_position_detail",
                extra={
                    "source_component": source_component,
                    "theme": sanitized_theme,
                },
            )
        except Exception as e:
            logger.error(f"Failed to decrypt theme: {e}")


@inject
def session_status_validator(
    chat_repository: ChatRepository = Depends(Provide[Container.chat_repository]),
    required_session_statuses=[
        ChatSessionStatus.APPLYING,
        ChatSessionStatus.REGISTERING,
    ],
):
    session_status = chat_repository.session_status()
    if session_status not in required_session_statuses:
        raise HTTPException(status_code=400, detail="Invalid session status")
