import json
import logging
from typing import overload, Type
from contextlib import AbstractContextManager
from copy import deepcopy
from pydantic import BaseModel
from sqlalchemy.orm import Session
from typing import Any, Callable

from domain.entities.user_profile import UserProfile
from utils.const import APPLY_POSITION_IDS_KEY, LOGGER_PREFIX
from utils.enum import EncryptKeyType
from utils.crypt import create_secret_key, decrypt, encrypt
from utils.logging import get_session_id


class UserRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )

    def _encrypt_basic_profile(self, user_profile: UserProfile) -> None:
        """
        miidas_registration_user_data.basic_profileを暗号化する
        """
        data = user_profile.miidas_registration_user_data
        if data is None:
            data = {}
            user_profile.miidas_registration_user_data = data

        basic_profile = data.get("basic_profile")
        if not isinstance(basic_profile, dict):
            return

        key = create_secret_key(EncryptKeyType.PROFILE)
        serialized = json.dumps(basic_profile)
        try:
            data["basic_profile"] = encrypt(key, serialized)
        except Exception:
            self._logger.exception("基本プロフィール情報の暗号化に失敗しました")
            raise

    def _decrypt_basic_profile(self, user_profile: UserProfile) -> None:
        """
        miidas_registration_user_data.basic_profileを復号化する
        """
        data = user_profile.miidas_registration_user_data
        if data is None:
            return

        basic_profile = data.get("basic_profile")
        if not isinstance(basic_profile, str):
            return

        key = create_secret_key(EncryptKeyType.PROFILE)
        try:
            decrypted = decrypt(key, basic_profile)
            data["basic_profile"] = json.loads(decrypted)
        except Exception:
            self._logger.exception("基本プロフィール情報の復号化に失敗しました")
            raise

    def update_job_search_filter(
        self,
        preference: dict[str, Any],
    ):
        """
        ユーザーの検索条件を保存する

        Args:
            preference: ユーザーの検索条件
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            user_profile = (
                session.query(UserProfile)
                .filter(
                    UserProfile.session_id == session_id,
                    UserProfile.deleted_at.is_(None),
                )
                .first()
            )
            if user_profile:
                user_profile.job_search_filter = preference
            else:
                user_profile = UserProfile(
                    session_id=session_id,
                    job_search_filter=preference,
                )
                session.add(user_profile)
            session.commit()

    def get_user_profile(
        self,
    ) -> UserProfile | None:
        """
        セッションIDからーザープロフィールを取得
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            user_profile = (
                session.query(UserProfile)
                .filter(
                    UserProfile.session_id == session_id,
                    UserProfile.deleted_at.is_(None),
                )
                .first()
            )
            if user_profile:
                self._decrypt_basic_profile(user_profile)
            return user_profile

    def save_user_profile(
        self,
        user_profile: UserProfile,
    ) -> bool:
        """
        ユーザープロフィールを保存
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            profile_to_save = deepcopy(user_profile)
            profile_to_save.session_id = session_id
            self._encrypt_basic_profile(profile_to_save)
            
            session.merge(profile_to_save)
            session.commit()
            return True

    @overload
    def update_miidas_registration_user_data(
        self,
        key: str,
        value: str,
    ) -> bool: ...

    @overload
    def update_miidas_registration_user_data(
        self,
        key: str,
        model: Type[BaseModel],
    ) -> bool: ...

    @overload
    def update_miidas_registration_user_data(
        self,
        data: dict,
    ) -> bool: ...

    def update_miidas_registration_user_data(
        self,
        key: str | None = None,
        value: str | None = None,
        model: BaseModel | None = None,
        data: dict | None = None,
    ) -> bool:
        """
        ユーザープロフィールのmiidas_registration_user_dataを更新する

        Args:
            key: キー
            value: 文字列値
            model: Pydanticモデル
            data: 辞書データ

        Returns:
            boolean: 更新成功ならTrue, 失敗ならFalse
        """
        user_profile = self.get_user_profile()
        if not user_profile:
            return False

        if user_profile.miidas_registration_user_data is None:
            user_profile.miidas_registration_user_data = {}

        if data is not None:
            user_profile.miidas_registration_user_data.update(data)
        elif key is not None:
            if model is not None:
                value = model.model_dump(mode="json", by_alias=True)  # Removed ()
                user_profile.miidas_registration_user_data[key] = value
            elif value is not None:
                user_profile.miidas_registration_user_data[key] = value
            else:
                return False
        else:
            return False

        return self.save_user_profile(user_profile)

    def add_apply_position(
        self,
        positions_id: str,
    ) -> bool:
        """
        応募ポジション追加

        Args:
            positions_id: ポジションID

        Returns:
            boolean: 更新成功ならTrue, 失敗ならFalse
        """
        user_profile = self.get_user_profile()
        if not user_profile:
            return False

        if user_profile.miidas_registration_user_data is None:
            user_profile.miidas_registration_user_data = {}

        if APPLY_POSITION_IDS_KEY not in user_profile.miidas_registration_user_data:
            user_profile.miidas_registration_user_data[APPLY_POSITION_IDS_KEY] = [
                positions_id
            ]
        elif isinstance(
            user_profile.miidas_registration_user_data[APPLY_POSITION_IDS_KEY], list
        ):
            if (
                positions_id
                not in user_profile.miidas_registration_user_data[
                    APPLY_POSITION_IDS_KEY
                ]
            ):
                user_profile.miidas_registration_user_data[
                    APPLY_POSITION_IDS_KEY
                ].append(positions_id)
        else:
            user_profile.miidas_registration_user_data[APPLY_POSITION_IDS_KEY] = [
                user_profile.miidas_registration_user_data[APPLY_POSITION_IDS_KEY],
                positions_id,
            ]

        return self.save_user_profile(user_profile)
