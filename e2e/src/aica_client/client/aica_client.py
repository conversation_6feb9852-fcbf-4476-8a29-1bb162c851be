import json
import time
import websockets
import backoff
from enum import Str<PERSON><PERSON>
from pydantic import BaseModel
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver


class PageName(StrEnum):
    NONE = ""
    CHAT = "Chat"
    POSITION_DETAIL = "PositionDetail"


class ChatResponseType(StrEnum):
    MESSAGE = "message"
    POSITION_SEARCH_RESULT = "position_search_result"
    ERROR = "error"
    END = "end"


class ChatStreamResponseModel(BaseModel):
    session_id: str
    message_id: str
    response_type: ChatResponseType
    position_id: str | None = None
    message: str


class AICAClient:
    def __init__(self, url, model, system_prompt, client_id):
        """
        初期化

        Args:
            url (str): キャリアアドバイザーのWebSocket URL
            model: 求職者LLM model
            system_prompt (str): 求職者システムプロンプト
            client_id (str): キャリアアドバイザークライアントID
        """
        self.url = url
        self.ws = None
        self.model = model
        self.system_prompt = system_prompt
        self.client_id = client_id
        self.job_seeker_agent = create_react_agent(
            model=model,
            checkpointer=InMemorySaver(),
            tools=[],
            prompt=system_prompt,
        )
        self.last_message = ""

    async def connect(self):
        """
        キャリアアドバイザーサーバーに接続する

        Returns:
            None
        """
        self.ws = await websockets.connect(
            self.url,
            ping_interval=None,
            ping_timeout=None,
        )

    async def send_structured_message_to_aica_agent(
        self,
        message,
        prev_page=PageName.NONE,
        current_page=PageName.CHAT,
        position_id=None,
    ):
        """
        キャリアアドバイザーへ送信する

        Args:
            message (str): 送信メッセージ
            prev_page (PageName): 遷移前ページ名（フロントエンドと同じやり方のため）
            current_page (PageName): 現在ページ名（フロントエンドと同じやり方のため）
            position_id (str, optional): ポジション詳細Posiの場合必要なポジションID

        Returns:
            None
        """
        if self.ws:
            data = {
                "prev_page": prev_page,
                "current_page": current_page,
                "position_id": position_id,
                "message": message,
            }
            await self.ws.send(json.dumps(data))

    async def receive_streaming_response_from_aica_agent(self):
        """
        キャリアアドバイザーサーバーから受信する

        Returns:
            tuple: (
                session_id: セッションID
                response_type: メッセージタイプ
                full_message: 受信したメッセージ
                first_msg_duration: 初めて受信までの経過時間
                total_duration: 全て受信までの経過時間
            )
        """
        response_type = ChatResponseType.ERROR
        messages = []
        start_time = time.time()
        first_message_time = None

        response_type = None
        while True:
            try:
                response = await self.ws.recv()
                data = ChatStreamResponseModel.model_validate_json(response)
                # print(f"data = {data}")

                match data.response_type:
                    case ChatResponseType.MESSAGE:
                        response_type = data.response_type
                        if first_message_time is None:
                            first_message_time = time.time()
                        messages.append(data.message)
                    case ChatResponseType.POSITION_SEARCH_RESULT:
                        response_type = data.response_type
                        messages.append(data.message)
                    case ChatResponseType.ERROR:
                        response_type = data.response_type
                        messages.append(data.message)
                        break
                    case ChatResponseType.END:
                        break
                    case _:
                        print(
                            f"サポートしないmessage_type: '{data.response_type}' (type: {type(data.response_type)})"
                        )
                        break
            except websockets.exceptions.ConnectionClosedError as e:
                print(f"{self.client_id} 接続切断: {e}")
                raise

        end_time = time.time()
        # ポジション検索結果のレスポンスや、ポジション詳細→メインチャットに戻る時のレスポンスの場合、streamingではない
        first_message_time = first_message_time or end_time
        first_msg_duration = first_message_time - start_time
        total_duration = end_time - start_time

        return (
            data.session_id,
            response_type,
            "".join(messages),
            first_msg_duration,
            total_duration,
        )

    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=10,
        giveup=lambda e: not (
            (
                e.__class__.__name__ == "ThrottlingException"
                or "ThrottlingException" in str(e)
            )
            and "Too many requests" in str(e)
        ),
    )
    async def send_message_to_job_seeker_agent(self, config, message):
        """
        求職者LLMにメッセージを送信する

        Args:
            config (dict): LLM設定
            message (str): メッセージ

        Returns:
            tuple: (
                agent_invoke_time: LLM応答までの経過時間
                response_message: LLM応答メッセージ
            )
        """
        agent_start_time = time.time()
        result = await self.job_seeker_agent.ainvoke(
            {"messages": [{"role": "user", "content": message}]},
            config,
        )
        agent_invoke_time = time.time() - agent_start_time
        # 会話回数が増えるほど、時間がかかるのかを確認するため
        # print(f"agent_start_time = {agent_start_time}, agent_invoke_time: {agent_invoke_time}")
        response_message = result["messages"][-1].content
        return agent_invoke_time, response_message

    async def close(self):
        """
        キャリアアドバイザーサーバーとの接続を切断する

        Returns:
            None
        """
        if self.ws:
            await self.ws.close()
