## 共通
TZ=
AICA_ENV=production
## Agent
AICA_AGENT_API_ENDPOINT=
AICA_AGENT_DB_HOST=
AICA_AGENT_DB_NAME=
AICA_AGENT_DB_PORT=
AICA_AGENT_DB_SSLMODE=
AICA_AGENT_DB_USER=
AICA_AGENT_MCP_ENDPOINT=
AICA_AGENT_PORT=
AICA_AGENT_MIIDAS_API_ENDPOINT=
OPENAI_AGENTS_DISABLE_TRACING=
OPENAI_AGENTS_DONT_LOG_MODEL_DATA=
## API
AICA_MCP_TOOL_API_DB_HOST=
AICA_MCP_TOOL_API_DB_NAME=
AICA_MCP_TOOL_API_DB_PORT=
AICA_MCP_TOOL_API_DB_SSLMODE=
AICA_MCP_TOOL_API_DB_USER=
AICA_MCP_TOOL_API_MIIDAS_DB_HOST=
AICA_MCP_TOOL_API_MIIDAS_DB_PORT=
AICA_MCP_TOOL_API_MIIDAS_DB_USER=
AICA_MCP_TOOL_API_MV2_HOST=
AICA_MCP_TOOL_API_MV2_INSECURE=
AICA_MCP_TOOL_API_MV2_PORT=
AICA_MCP_TOOL_API_PORT=
AICA_MCP_TOOL_BATCH_DB_HOST=
AICA_MCP_TOOL_BATCH_DB_NAME=
AICA_MCP_TOOL_BATCH_DB_PORT=
AICA_MCP_TOOL_BATCH_DB_SSLMODE=
AICA_MCP_TOOL_BATCH_DB_USER=
AICA_MCP_TOOL_BATCH_MIIDAS_DB_HOST=
AICA_MCP_TOOL_BATCH_MIIDAS_DB_PORT=
AICA_MCP_TOOL_BATCH_MIIDAS_DB_USER=
MIIDAS_S3_USER_ASSETS_ENDPOINT=
## MCP
AICA_MCP_API_SERVER=
AICA_MCP_DB_HOST=
AICA_MCP_DB_NAME=
AICA_MCP_DB_PORT=
AICA_MCP_DB_SSLMODE=
AICA_MCP_DB_USER=
AICA_MCP_PORT=
