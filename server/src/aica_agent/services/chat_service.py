from dataclasses import asdict, is_dataclass
from datetime import datetime
from enum import StrEnum
import json
import time
from typing import Any, Async<PERSON><PERSON>ator
import uuid

from openai.types.responses import ResponseTextDeltaEvent, ResponseOutputTextParam
from openai.types.responses.response_input_item_param import Message
from openai.types.responses.response_input_text_param import ResponseInputTextParam
from agents.items import ToolCallItemTypes
from agents import (
    Agent,
    HandoffCallItem,
    HandoffOutputItem,
    MessageOutputItem,
    ReasoningItem,
    RunItem,
    Runner,
    TResponseInputItem,
    ToolCallItem,
    ToolCallOutputItem,
    RunResultStreaming,
)

from domain.entities.chat_history import ChatHistory
from domain.entities.chat_session import ChatSessionStatus
from repositories.llm_repo import AgentName, LLMRepository
from repositories.position_repo import PositionRepository
from repositories.chat_repo import ChatRepository
from repositories.user_repo import UserRepository
from services.base_service import BaseService
from utils.chat_request import ChatRequestModel, ChatRequestType
from utils.chat_response import ChatStreamResponse, ChatStreamResponseModel
from utils.const import APPLY_POSITION_IDS_KEY
from utils.enum import LLMMessageRole, LocationType, PageName
from utils.logging import get_session_id, set_session_id


def _json_default(obj: object) -> Any:
    if is_dataclass(obj):
        return asdict(obj)
    if hasattr(obj, "model_dump"):
        return obj.model_dump()  # type: ignore[attr-defined]
    if hasattr(obj, "dict"):
        return obj.dict()  # type: ignore[attr-defined]
    if hasattr(obj, "__dict__"):
        return obj.__dict__
    return str(obj)


DEFAULT_ERROR_MESSAGE = (
    "大変混み合っておりますので、しばらく経ってからリロードしてご利用ください。"
)
POSITION_SEARCH_FAKE_RESULT = "ポジション検索が実行されました。ユーザーには別の手段で求人の検索結果を見せていますが、ユーザーから条件変更や再度見たいとの要望があれば、検索条件の差異に関わらず、再度このツールを実行してください。"

MAIN_CHAT_KEY = "MAIN"

POSITION_DETAIL_INQUIRY_START_PROMPT = """指定ポジションは下記となります。ユーザーに求人情報について回答する時に、下記の内容を確認してください。

#求人情報
%s
#会社詳細情報
%s
#事業詳細情報
%s
#パラメータの補足解説\n下記は、各情報について、*どのパラメータを確認すると見つけかりやすいか*を補足解説しています。
【必須】

##求人情報についての補足
どのような人が歓迎されるか: HREvaluationCompetency
どのような所がこの会社の魅力か: PR
会社名: Company要素内のName

##会社詳細情報についての補足
会社名: Prefectureの前にあるNameを確認してください。
何をしている会社か: Introduction,PR
特別な評価制度はあるか: HREvaluationSpecialSystem
福利厚生はどうか: Welfare,WelfareOther

##事業詳細についての補足
この会社の業界(業種)での立ち位置がわかります。
有形商材か無形商材か: Tangibleness"""

POSITION_CHAT_DETAIL_MESSAGE_ID_PREFIX = "position_detail_chat_summary_"


# フック必要なツール名
class ToolName(StrEnum):
    # ポジション検索
    POSITION_SEARCH = "search_job_postings"
    # ポジション検索条件保存
    USER_PREFERENCE = "save_user_preference"
    # 応募
    APPLICATION = "form_application"
    # 登録
    REGISTRATION = "form_registration"


class ChatService(BaseService):
    """
    LLM会話サービス
    """

    def __init__(
        self,
        llm_repository: LLMRepository,
        chat_repository: ChatRepository,
        position_repository: PositionRepository,
        user_repository: UserRepository,
    ) -> None:
        """
        インスタンス初期化

        Args:
            llm_repository: LLMモデルリポジトリ
            chat_repository: 会話履歴リポジトリ
            position_repository: ポジションデータリポジトリ
        """
        super().__init__()

        # LLMプロバイダー（OpenAI、Bedrock）
        self._provider: str | None = None
        self._llm_repository = llm_repository
        self._chat_repository = chat_repository
        self._position_repository = position_repository
        self._user_repository = user_repository
        self._agents: dict[str, Agent] = {}
        # 今会話しているエージェント
        self._active_agent_name: str = None
        # セッション作成済みフラグ。※挨拶だけの会話は保存しないので、このフラグを設けています。
        # True：chat_sessionデータがすでに作られている
        # False：新しいセッションなので、まだchat_sessionデータが作られていない。
        self._session_created = False
        self._conversation_to_save_when_session_created = []
        # DB保存フラグ。※挨拶だけの会話は保存しないので、このフラグを設けています。
        # LLMとの会話は1回だけの場合、挨拶扱いとしてDBデータは保存しない
        # 2回目から、初めての挨拶を含めてDBにデータを保存します。
        # True：LLMとの会話は2回以上の場合、保存すべき
        # False：まだ挨拶だけなので、DB保存はしない
        self._should_save = False

        self._previous_response_ids: dict[str, str] = {}
        # 現在ターンの会話
        self._conversation: dict[str, list] = {}
        # すべての過去会話履歴
        self._chat_histories: dict[str, list[ChatHistory]] = {MAIN_CHAT_KEY: []}
        self._chat_key: str = MAIN_CHAT_KEY
        self._position_id: str | None = None

    async def init_session(
        self,
        provider: str,
    ) -> tuple[ChatSessionStatus, bool, str | None]:
        """
        セッション初期化。※セッションはwebsocket接続毎に１つ

        Args:
            provider: LLMプロバイダー

        Returns:
            以下を含むtuple:
            - ChatSessionStatus: セッションのステータス
            - bool: 新規セッションかどうか
            - str | None: 既存セッションの場合は最後のメッセージID、新規セッションの場合はNone
        """
        self._provider = provider

        agents = self._llm_repository.clone_agents(self._provider)
        for agent_name, (agent, default_agent) in agents.items():
            self._agents[agent_name] = agent
            if default_agent:
                self._active_agent_name = agent_name
        self._toolcall_trace_message = {
            "type": "message",
            "role": LLMMessageRole.DEVELOPER,
            "content": f"""### ツール呼び出すときのパラメータについて
セッションID：{get_session_id()}を利用してください。
リクエストID：なるべく重複しないよう任意のuuidを生成して、リクエストごとにユニークな値を設定してください。
""",
        }

        try:
            (chat_session, exists) = self._chat_repository.init_chat_session()
            if chat_session:
                # DBにセッションデータがあるので、セッション作成済みフラグをTrueにする。
                self._session_created = True
                if chat_session.histories:

                    # 以前の会話から続くため、会話履歴をロードして、LLMに渡す。
                    self._chat_histories, self._conversation = (
                        self._convert_to_llm_messages(chat_session.histories)
                    )

                    # 既存セッションの再開なので、前回のアクティブエージェントを続いて利用する。
                    # POSITION_GUIDEでないAgentを探す
                    self._active_agent_name = self._find_last_non_position_guide_agent()

                    # DB保存済みセッションの再開なので、つまりLLMと2回以上会話しているので、DB保存フラグをTrueにする。
                    self._should_save = True

                if chat_session.user_profile:
                    self._position_repository.save_user_preference(
                        chat_session.user_profile.job_search_filter,
                    )
            elif exists:
                set_session_id(str(uuid.uuid4()))

            if MAIN_CHAT_KEY not in self._conversation or not self._conversation.get(
                MAIN_CHAT_KEY
            ):
                self._conversation[MAIN_CHAT_KEY] = [
                    self._toolcall_trace_message,
                ]

            if chat_session:
                is_new_session = False
                last_message_id = next((h.message_id for h in reversed(chat_session.histories or []) if h.message_id), None)
                return chat_session.status, is_new_session, last_message_id
            else:
                is_new_session = not exists
                return ChatSessionStatus.CHATTING, is_new_session, None
        except Exception as e:
            self.logger.exception(f"セッション初期化失敗: {e}")
            return ChatSessionStatus.ERROR, False, None

    def _convert_to_llm_messages(
        self,
        histories: list[ChatHistory],
    ) -> tuple[dict[str, list[ChatHistory]], dict[str, list]]:
        """
        DB履歴でデータからLLM会話履歴作成。

        Args:
            histories: DB履歴でデータ

        Returns:
            chatキー毎の(DB会話履歴, LLM用会話履歴)
        """
        chat_histories: dict[str, list[ChatHistory]] = {}
        all_messages: dict[str, list] = {}

        for history in histories:
            if history.position_id:
                history_key = str(history.position_id)
                self._create_position_agent_if_not_exist(self._position_id)
            else:
                history_key = MAIN_CHAT_KEY

            chat_histories.setdefault(history_key, []).append(
                ChatRepository.clone_chat_history(history)
            )
            messages = all_messages.setdefault(history_key, [])

            if history.role in [LLMMessageRole.USER, LLMMessageRole.DEVELOPER]:
                messages.append(
                    {
                        "type": "message",
                        "role": history.role,
                        "content": history.content,
                    }
                )
            elif history.role == LLMMessageRole.ASSISTANT:
                messages.append(
                    {
                        "type": "message",
                        "role": history.role,
                        "content": [
                            ResponseOutputTextParam(
                                type="output_text",
                                text=history.content,
                            )
                        ],
                    }
                )
            elif history.role in [LLMMessageRole.TOOL, LLMMessageRole.HANDOFF]:
                messages.append(
                    {
                        "type": "function_call",
                        "call_id": history.tool_call_id,
                        "name": history.tool_name,
                        "arguments": json.dumps(history.tool_input),
                    }
                )

                output = history.content
                if history.tool_name == ToolName.POSITION_SEARCH:
                    # ポジション検索実行結果ではなく、フェイク結果をLLMに渡す
                    output = POSITION_SEARCH_FAKE_RESULT

                messages.append(
                    {
                        "type": "function_call_output",
                        "call_id": history.tool_call_id,
                        "output": output,
                    }
                )
            elif history.role == LLMMessageRole.REASONING:
                # FIXME: 必要？
                pass
            else:
                self.logger.error(f"Unsupported message role: {history}")
        return chat_histories, all_messages

    async def chat(
        self,
        input: ChatRequestModel,
    ) -> AsyncGenerator[ChatStreamResponseModel, None]:
        """
        ユーザーインプットをLLMに渡して、レスポンスを返す。
        インプットごとに呼び出される

        Args:
            input: フロントからのユーザーインプット

        Returns:
            LLMレスポンス
        """
        self.logger.debug("Chat: %s", input.model_dump(mode="json"))

        encrypted_position_id = input.position_id or None
        message = input.message
        session_status = self._chat_repository.session_status()
        if not session_status:
            session_status = ChatSessionStatus.CHATTING

        if (
            session_status
            in (ChatSessionStatus.REGISTERING, ChatSessionStatus.APPLYING)
            and input.request_type == ChatRequestType.START
        ):
            # 面談応募／登録中は、メインチャットできないため、
            # 会話開始リクエストの場合、セッションステータスを返すだけで良い
            yield ChatStreamResponse(
                request_type=input.request_type,
            ).create_end_response(
                session_status,
            )
            return

        try:
            if encrypted_position_id:
                self._position_id = self._position_repository.decrypt_id(
                    encrypted_position_id
                )
                if not self._position_id:
                    # IMPOSSIBLE：セッションタイムアウトの場合発生しうるが、セッションタイムアウトの場合、そもそもポジション詳細に入れないなので、ここまでこれないはず
                    raise ValueError(
                        f"ポジションIDが見つかりません: {encrypted_position_id}"
                    )
                self._chat_key = self._position_id
            else:
                self._position_id = None
                self._chat_key = MAIN_CHAT_KEY

            await self._prepare_for_chat_turn(
                input,
            )

            if self._chat_key not in self._conversation:
                yield ChatStreamResponse(
                    request_type=input.request_type,
                    position_id=encrypted_position_id,
                ).create_end_response(
                    session_status,
                )
                return

            role = self._get_message_role(input.request_type)

            self._conversation[self._chat_key].append(
                Message(
                    type="message",
                    role=role,
                    content=[ResponseInputTextParam(type="input_text", text=message)],
                ),
            )

            if input.request_type == ChatRequestType.START:
                input_message_id = input.current_message_id
                input.current_message_id = None

                async for response in self.load_previous_chat_histories(
                    input=input,
                    session_status=session_status,
                    send_end_response=False,
                ):
                    if response:
                        yield response

                input.current_message_id = input_message_id

            result = None
            while True:
                # ポジション検索ツールからバリデーションエラーを返してくれているかどうか
                position_search_validation_error = False
                user_profile_to_save: dict | None = None
                tool_calls: dict[ToolName, ToolCallItemTypes] = {}

                chat_response = ChatStreamResponse(
                    request_type=input.request_type,
                    position_id=encrypted_position_id,
                )

                self.logger.debug(f"last message: {self._conversation[self._chat_key][-1]}")
                start_time = time.time()
                run_result = Runner.run_streamed(
                    starting_agent=self._get_agent(self._active_agent_name),
                    input=self._conversation[self._chat_key],
                    previous_response_id=self._previous_response_ids.get(
                        self._chat_key
                    ),
                )

                async for event in run_result.stream_events():
                    if event.type == "raw_response_event":
                        if isinstance(event.data, ResponseTextDeltaEvent):
                            # Streaming response from LLM
                            yield chat_response.create_agent_message_response(
                                event.data.item_id,
                                event.data.delta,
                                session_status,
                            )
                    elif event.type == "run_item_stream_event":
                        if event.item.type == "tool_call_item":
                            # Tool call
                            user_profile_to_save = self._handle_tool_call_item(
                                event.item,
                                tool_calls,
                            )
                        elif event.item.type == "tool_call_output_item":
                            # Tool call result
                            parsed_output = self._parse_tool_output(
                                event.item.raw_item["output"]
                            )

                            if "Message" in parsed_output:
                                # "Message"キーが入っている場合、失敗とみなす
                                self.logger.error(f"ツール実行失敗: {event.item}")
                                position_search_validation_error = (
                                    event.item.raw_item["call_id"]
                                    == tool_calls.get(ToolName.POSITION_SEARCH).call_id
                                )
                            else:
                                tool_name_key = next(
                                    (
                                        tool_name
                                        for tool_name, item in tool_calls.items()
                                        if item.call_id
                                        == event.item.raw_item["call_id"]
                                    ),
                                    None,
                                )
                                if tool_name_key:
                                    match tool_name_key:
                                        case ToolName.POSITION_SEARCH:
                                            # ポジション検索結果
                                            # 分析用のログ出力
                                            all_positions = parsed_output.get(
                                                "AllPositionIds", []
                                            )
                                            self.logger.info(
                                                "analyze_position_search_results",
                                                extra={
                                                    "count": (
                                                        len(all_positions)
                                                        if all_positions
                                                        else 0
                                                    )
                                                },
                                            )
                                            # ポジション検索ツール結果分析し、結果をLLMに渡さず、直接フロントに返す。
                                            position_search_result = self._position_repository.process_position_search_result(
                                                parsed_output,
                                            )
                                            yield chat_response.create_tool_result_response(
                                                tool_calls.get(
                                                    ToolName.POSITION_SEARCH
                                                ).id,
                                                position_search_result,
                                                session_status,
                                            )
                                        case ToolName.USER_PREFERENCE:
                                            # ポジション検索条件保存
                                            parsed_output.pop("MessageToLLM", None)
                                            user_profile_to_save = (
                                                parsed_output if parsed_output else None
                                            )
                                        case ToolName.APPLICATION:
                                            # 応募
                                            # TODO: ChatSessionStatus.CHATTING以外の場合、フロントの方の動きが違う
                                            if (
                                                input.current_page
                                                == PageName.POSITION_DETAIL
                                            ):
                                                # ポジション詳細ページしかできない
                                                real_id = self._position_repository.decrypt_id(
                                                    encrypted_position_id,
                                                )
                                                self._user_repository.update_miidas_registration_user_data(
                                                    APPLY_POSITION_IDS_KEY,
                                                    [real_id],
                                                )

                                                self._chat_repository.update_session_status(
                                                    ChatSessionStatus.APPLYING,
                                                )

                                                session_status = (
                                                    ChatSessionStatus.APPLYING
                                                )
                                        case ToolName.REGISTRATION:
                                            # 登録
                                            # TODO: ChatSessionStatus.CHATTINGのときのみ可能
                                            # 分析用のログ出力
                                            current_page = input.current_page
                                            if (
                                                current_page == PageName.CHAT
                                                or current_page
                                                == PageName.POSITION_DETAIL
                                            ):
                                                self.logger.info(
                                                    "analyze_registration",
                                                    extra={"page": current_page},
                                                )

                                            session_status = (
                                                ChatSessionStatus.REGISTERING
                                            )
                                            self._chat_repository.update_session_status(
                                                session_status,
                                            )

                end_time = time.time()
                elapsed = end_time - start_time
                self.logger.info(
                    f"chat_service.py: chat turn took {elapsed:.2f} seconds."
                )

                result = run_result
                token_usage = result.context_wrapper.usage
                token_usage_str = json.dumps(token_usage, default=_json_default)
                self.logger.info(
                    "analyze_token_usage",
                    extra={
                        "input_tokens": token_usage.input_tokens,
                        "cached_tokens": token_usage.input_tokens_details.cached_tokens,
                        "output_tokens": token_usage.output_tokens,
                        "usage": token_usage_str,
                    },
                )
                yield chat_response.create_token_usage_response(
                    f"\nToken Usage: {token_usage_str}",
                    session_status,
                )
                self._previous_response_ids[self._chat_key] = result.last_response_id
                self._save_chat(
                    input,
                    result,
                    user_profile_to_save,
                    session_status,
                )

                if ToolName.POSITION_SEARCH in tool_calls:
                    # ポジション詳細お問い合わせには、ポジション検索ツールが呼ばれないので、chat_keyは常にMAIN_CHAT_KEYのはず
                    # run_result.to_input_list(): 全部会話履歴
                    # run_result.new_items: 現在のラウンド

                    # ReponseAPIの仕様かもしれない、stop_at_tool_namesのツールが呼びされた場合、
                    # もし同時にstop_at_tool_namesに入ってないツールも呼び出されたら、そのツールの結果も次に一緒に渡さないといけなさそう。
                    # エラー例：save_user_preferenceとsearch_job_postingsは同時に呼ばれまして、search_job_postingsにバリデーションエラーが発生しました場合
                    # もし、ここはsearch_job_postingsの結果だけ取って次のリクエストで送ったら、下記のエラーが発生します。
                    # Error code: 400 - {'error': {'message': 'No tool output found for function call call_NUNaX6PmPoWjl3u7lzn8jz8o.', 'type': 'invalid_request_error', 'param': 'input', 'code': None}}
                    # call_NUNaX6PmPoWjl3u7lzn8jz8oはsave_user_preferenceです。
                    # FIXME: 不思議なのは、OpenAIトレースの方は、実は、save_user_preferenceの結果が入ってます。よくわからない。
                    # なので、どこかの勘違いかもしれないが、まだわからない
                    self._conversation[self._chat_key] = []
                    for item in run_result.to_input_list():
                        if (
                            item.get("type", None) == "function_call_output"
                            and "call_id" in item
                        ):
                            if (
                                item["call_id"]
                                == tool_calls[ToolName.POSITION_SEARCH].call_id
                                and not position_search_validation_error
                            ):
                                # フェークのポジション検索結果を次ターンでユーザーメッセージと一緒に送るため
                                self._conversation[self._chat_key].append(
                                    {
                                        "type": "function_call_output",
                                        "call_id": event.item.raw_item["call_id"],
                                        "output": POSITION_SEARCH_FAKE_RESULT,
                                    }
                                )
                            else:
                                self._conversation[self._chat_key].append(item)
                if not position_search_validation_error:
                    break
        except Exception as e:
            self.logger.exception(e)
            # chat_responseはまだない可能性があるので
            yield ChatStreamResponse(
                request_type=input.request_type,
                position_id=encrypted_position_id,
            ).create_error_response(
                DEFAULT_ERROR_MESSAGE,
                session_status,
            )
            return

        yield chat_response.create_end_response(
            session_status,
        )

    # FIXME: バックエンドで処理していますが、LLMがサマル作成に時間がかかる場合、会話をブロックしてしまいます。
    # ユーザーがメッセージを送っても、アプリが反応しなくなるように見えます。
    async def summarize_position_detail_chat(
        self,
    ) -> ChatSessionStatus:
        position_chat_histories = self._chat_histories.get(self._position_id, [])
        if not position_chat_histories:
            return self._chat_repository.session_status()

        summary_text = await self._llm_repository.summarize_position_detail_chat(
            position_chat_histories,
        )
        if not summary_text:
            self.logger.error(f"OpenAI summary response contained no text: {get_session_id(), self._position_id}")
            return self._chat_repository.session_status()

        # message_idを生成（タイムスタンプを使用）
        timestamp = int(datetime.now().timestamp())
        message_id = (
            f"{POSITION_CHAT_DETAIL_MESSAGE_ID_PREFIX}{self._position_id}_{timestamp}"
        )

        # LLMから返答をもらった後DBに保存するのは、ユーザーからのメッセージとLLMからの返答のみ、
        # また、ポジション詳細からメインチャットに戻った後、ユーザーはもうメッセージを送ってくれない可能性もあります。
        # なので、いまサマリをDBに保存するしかない
        self._chat_repository.add_chat_history(
            ChatHistory(
                session_id=get_session_id(),
                position_id=None,
                active_agent=AgentName.POSITION_GUIDE,
                message_id=message_id,
                role=LLMMessageRole.ASSISTANT,
                content=summary_text,
            )
        )

        # ポジション詳細から戻ってきた時点では、LLMからの返答が必要ないので、いますぐさまった情報をLLMに送らず、メインチャット会話履歴を入れたら終わり
        # 次のメインチャットでのユーザーからのメッセージが来たら、一緒にLLMに送る。
        self._conversation[MAIN_CHAT_KEY].append(
            {
                "type": "message",
                "role": LLMMessageRole.DEVELOPER.value,
                "content": [
                    ResponseOutputTextParam(
                        type="output_text",
                        text=summary_text,
                    )
                ],
            }
        )

        return self._chat_repository.session_status()

    async def _prepare_for_chat_turn(
        self,
        input: ChatRequestModel,
    ):
        """
        フロントからのINPUT解析

        Args:
            input: フロントからのINPUT
            ChatSessionStatus: セッションステータス
        """
        session_id = get_session_id()
        current_page = input.current_page
        encrypted_position_id = input.position_id or None
        message = input.message

        if current_page == PageName.CHAT:
            # `/chat`
            # OR
            # if prev_page == PageName.POSITION_DETAIL:
            # `/positions/{position_id}` => `/chat`
            if self._active_agent_name == AgentName.POSITION_GUIDE:
                # ポジション詳細画面でチャットしてからメインチャットに戻ってきた場合、強制的にアクティブAgentを戻します。
                self._active_agent_name = self._find_last_non_position_guide_agent()
        elif current_page == PageName.POSITION_DETAIL and encrypted_position_id:
            # `/chat` => `/positions/{position_id}`
            self._active_agent_name = AgentName.POSITION_GUIDE
            self._create_position_agent_if_not_exist(self._position_id)

            if not self._conversation.get(self._chat_key):
                position_detail, company_detail, business_detail, error_message = (
                    await self._get_position_detail(encrypted_position_id)
                )

                if error_message:
                    self.logger.error(error_message)
                    raise ValueError(error_message)

                # 当初の想定ではポジション詳細をLLMに伝えるにはシステムプロンプトを利用できるようになるのはポジション詳細Agentクローンのメリットの1つかと思ってましたが、
                # 中村さんと相談（https://miidas-dev.slack.com/archives/C08BU50QS3Y/p1751329093253549?thread_ts=1750744115.699779&cid=C08BU50QS3Y）して、
                # 「求人詳細については一旦今のまま(developerロールでのメッセージ扱い)が安定しそうです！」なので、システムプロンプトの利用はをやめました。
                message = POSITION_DETAIL_INQUIRY_START_PROMPT % (
                    json.dumps(position_detail),
                    json.dumps(company_detail),
                    json.dumps(business_detail),
                )

                self._conversation[self._chat_key] = [
                    self._toolcall_trace_message,
                    {
                        "type": "message",
                        "role": LLMMessageRole.DEVELOPER,
                        "content": message,
                    },
                ]
                chat_histories = [
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=AgentName.POSITION_GUIDE,
                        message_id=None,
                        role=self._toolcall_trace_message["role"],
                        content=self._toolcall_trace_message["content"],
                    ),
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=AgentName.POSITION_GUIDE,
                        message_id=None,
                        role=LLMMessageRole.DEVELOPER,
                        content=message,
                    ),
                ]
                # 現在DB保存対象となるのは、LLMから返答をもらった後の、ユーザーからのメッセージとLLMからの返答のみです。
                # つまり、そのときは上記2つのメッセージは保存対象ではないので、ここでDBに保存します。
                self._save_chat_histories(chat_histories)
        else:
            error_msg = f"Unknown page: {current_page}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def _parse_tool_output(
        self,
        output: str,
    ) -> dict:
        """
        ツール結果解析

        Args:
            output: 生のツール結果

        Returns:
            解析後のツール結果
        """
        self.logger.debug(f"ツールのoutput: {output}")
        outer_result = json.loads(output)
        inner_result_str = outer_result.get("text")
        if not isinstance(inner_result_str, str):
            raise Exception(
                f"ツールのoutputの'text'フィールドは文字列ではない: {output}"
            )
        try:
            return json.loads(inner_result_str)
        except (json.JSONDecodeError, TypeError) as e:
            self.logger.exception(
                f"ツールのoutputの'text'フィールドのJSON解析に失敗しました: {e}. 入力: {inner_result_str}"
            )
            return {}

    def _handle_tool_call_item(
        self,
        item: ToolCallItem,
        tool_calls: dict[ToolName, ToolCallItemTypes],
    ):
        """
        指定ツールコールの記録・処理

        Args:
            item: ツールコール入力
            tool_calls: 該当会話中のツールコール

        Returns:
        """
        try:
            tool_name = ToolName(item.raw_item.name)
            tool_calls[tool_name] = item.raw_item
        except ValueError:
            # 処理対象外ツールなので、無視
            return None

        if tool_name == ToolName.POSITION_SEARCH:
            try:
                # ポジション検索条件解析
                tool_input = json.loads(item.raw_item.arguments)
            except (json.JSONDecodeError, TypeError) as e:
                self.logger.exception(
                    f"ポジション検索条件（ツールパラメータ）解析失敗しました: {e}"
                )
                return

            # ポジション検索条件保存
            preference_input = tool_input.copy()
            preference_input.pop("SessionID", None)
            preference_input.pop("RequestID", None)
            return preference_input

        return None

    def _save_chat(
        self,
        input: ChatRequestModel,
        result: RunResultStreaming,
        user_profile_to_save: dict,
        session_status: ChatSessionStatus,
    ):
        """
        会話履歴のDB保存

        Args:
            input: ユーザー入力
            result: LLM回答
            user_profile_to_save: ユーザー検索条件
            session_status: セッションステータス
        """
        if self._should_save:
            if not self._session_created:
                # セッションはまだ作成されていないので、セッションを作成して、最初の挨拶と今回のユーザインプットをDBに保存する
                # 今回LLMレスポンスは_save_llm_messagesより保存します。
                self._create_session(
                    self._conversation_to_save_when_session_created
                    + result.to_input_list(),
                    session_status,
                )
            else:
                # セッションは作成済みなので、ユーザーからのメッセージだけを保存する
                self._save_user_or_developer_message(input)
                # LLMの返答を保存する
                # result.to_input_list()は普通のツールコールか、ハンドオフか区別できないので、result.new_itemsを使います。
                self._save_llm_messages(result.new_items)

            # https://miidas-dev.slack.com/lists/TJWTV7T7C/F08BU50QS3Y?record_id=Rec09CLQW1HL2
            # ユーザから初めてのメッセージより、ポジション検索条件保存が発せする可能性があります。
            # その場合、セッションはまだ保存されていないので、下記エラーが発生します。
            # DBセッションロールバック: (psycopg.errors.ForeignKeyViolation) insert or update on table \"user_profiles\" violates foreign key constraint \"user_profiles_session_id_fkey\"
            # そのため、ポジション検索条件はセッション保存後行うようにします。
            if user_profile_to_save:
                self._position_repository.save_user_preference(
                    user_profile_to_save,
                )
                self._user_repository.update_job_search_filter(
                    user_profile_to_save,
                )
        else:
            # 初めは挨拶なので、セッション作成はしないが、それ以降会話は続く場合保存するので、DB保存フラグをTrueにする
            self._should_save = True
            self._conversation_to_save_when_session_created = result.to_input_list()

        # ResponseAPIなので
        self._conversation[self._chat_key].clear()

    def _create_session(
        self,
        conversation_history: list[TResponseInputItem],
        session_status: ChatSessionStatus,
    ):
        """
        挨拶以上の会話した場合、初めてセッションデータをDB保存する

        Args:
            conversation_history: 会話履歴
        """
        session_id = get_session_id()
        self._chat_repository.create_chat_session(
            session_status=session_status,
        )
        # 最初の挨拶メッセージ保存
        chat_histories: list[ChatHistory] = []
        for message in conversation_history:
            if message["type"] == "message":
                chat_histories.append(
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent_name,
                        message_id=message["id"] if "id" in message else None,
                        role=message["role"],
                        content=(
                            message["content"]
                            if isinstance(message["content"], str)
                            else message["content"][0]["text"]
                        ),
                    )
                )
            elif message["type"] == "function_call":
                chat_histories.append(
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent_name,
                        message_id=message["id"] if "id" in message else None,
                        role=LLMMessageRole.TOOL,
                        tool_call_id=message["call_id"],
                        tool_name=message["name"],
                        tool_input=json.loads(message["arguments"]),
                    )
                )
            elif message["type"] == "function_call_output":
                tool_call_id = message["call_id"]
                history = [
                    history
                    for history in chat_histories
                    if history.tool_call_id == tool_call_id
                ]
                if history:
                    history[0].content = message["output"]
                else:
                    self.logger.error(f"tool call id {tool_call_id} is NOT found.")
            elif message["type"] == "reasoning":
                chat_histories.append(
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent_name,
                        message_id=message["id"] if "id" in message else None,
                        role=LLMMessageRole.REASONING,
                        content=json.dumps(message["summary"]),
                    )
                )
            else:
                self.logger.error(f"Unsupported message type: {message}")

        self._save_chat_histories(chat_histories)
        self._session_created = True

    def _get_message_role(
        self,
        request_type: ChatRequestType,
    ) -> LLMMessageRole:
        """
        開始または再開のメッセージであればDeveloper roleにして、
        会話履歴に表示されないようにする
        """
        if request_type in [ChatRequestType.START, ChatRequestType.RESTART_CHAT]:
            return LLMMessageRole.DEVELOPER
        else:
            return LLMMessageRole.USER

    def _save_user_or_developer_message(
        self,
        input: ChatRequestModel,
    ):
        """
        ユーザーまたはデベロッパーのインプットをDB保存する

        Args:
            input: ユーザーまたはデベロッパーのインプット
        """
        role = self._get_message_role(input.request_type)

        self._chat_repository.add_chat_history(
            ChatHistory(
                session_id=get_session_id(),
                position_id=self._position_id,
                active_agent=self._active_agent_name,
                message_id=input.current_message_id,
                role=role,
                content=input.message,
            )
        )

    def _save_llm_messages(
        self,
        items: list[RunItem],
    ):
        """
        ユーザーインプットをDB保存する

        Args:
            items: LLM回答
        """
        session_id = get_session_id()
        chat_histories: list[ChatHistory] = []

        for item in items:
            self._active_agent_name = item.agent.name
            if isinstance(item, MessageOutputItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent_name,
                        message_id=item.raw_item.id,
                        role=LLMMessageRole.ASSISTANT,
                        content=item.raw_item.content[0].text,
                    )
                )
            elif isinstance(item, HandoffCallItem) or isinstance(item, ToolCallItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent_name,
                        message_id=item.raw_item.id,
                        role=(
                            LLMMessageRole.TOOL
                            if isinstance(item, ToolCallItem)
                            else LLMMessageRole.HANDOFF
                        ),
                        tool_call_id=item.raw_item.call_id,
                        tool_name=item.raw_item.name,
                        tool_input=json.loads(item.raw_item.arguments),
                    )
                )
            elif isinstance(item, HandoffOutputItem) or isinstance(
                item, ToolCallOutputItem
            ):
                history = [
                    history
                    for history in chat_histories
                    if history.tool_call_id == item.raw_item["call_id"]
                ]
                if history:
                    history[0].content = item.raw_item["output"]
            elif isinstance(item, ReasoningItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent_name,
                        message_id=item.raw_item.id,
                        role=LLMMessageRole.REASONING,
                        content=json.dumps(item.raw_item.summary),
                    )
                )
            else:
                self.logger.error(f"Unsupported item type: {item}")

        self._save_chat_histories(chat_histories)

    async def _get_position_detail(
        self,
        encrypted_position_id: str,
    ) -> tuple[dict, dict, dict, str]:
        """
        キャッシュからポジション詳細、会社詳細、業界詳細を取得

        Args:
            encrypted_position_id: ポジションUUID

        Returns:
            成功時：ポジション詳細、会社詳細、業界詳細
            失敗時：エラーメッセージ
        """
        postion_detail = await self._position_repository.get_position_detail(
            encrypted_position_id,
        )
        if not postion_detail:
            return (
                None,
                None,
                None,
                f"ポジション詳細が見つからなかった: {encrypted_position_id}",
            )

        company_detail = await self._position_repository.get_company_detail(
            encrypted_position_id,
        )
        if not company_detail:
            return (
                None,
                None,
                None,
                f"会社詳細が見つからなかった: {encrypted_position_id}",
            )

        business_detail = await self._position_repository.get_business_detail(
            encrypted_position_id,
        )
        if not business_detail:
            return (
                None,
                None,
                None,
                f"業界詳細が見つからなかった: {encrypted_position_id}",
            )

        return (postion_detail, company_detail, business_detail, None)

    def _get_agent(self, agent_name: str) -> Agent:
        """
        エージェントを取得する。

        Args:
            agent_name: エージェント名

        Returns:
            エージェント
        """
        agent = (
            self._agents.get(self._position_id)
            if agent_name == AgentName.POSITION_GUIDE
            else self._agents.get(agent_name)
        )
        if not agent:
            raise Exception(f"Agent not found: {agent_name}")

        return agent

    # TODO: 複数のタブやWindowsで異なるポジション詳細が見れると思ってましたので、ポジション詳細Agentをクローンをしていますが、
    # 試したところ、確かポジション詳細が見れますが、websocketはもう利用できなくなります。
    # なので、複数タブやWindowsでのポジション詳細確認を辞めるか、websocketを利用できるようにするかを一度検討の必要があるかも
    # 前者（やめる）のほうがやりやすいかと思われます。
    # https://miidas-dev.slack.com/lists/TJWTV7T7C/F08BU50QS3Y?record_id=Rec093HHSAVAS
    def _create_position_agent_if_not_exist(self, position_id: int):
        """
        ポジションエージェントがない場合、作成する。

        Args:
            position_id: ポジションID

        Returns:
            None
        """
        position_id_str = str(position_id)
        if position_id_str not in self._agents:
            self._agents[position_id_str] = self._agents.get(
                AgentName.POSITION_GUIDE,
            ).clone()

    def _save_chat_histories(
        self,
        chat_histories,
    ):
        """
        会話履歴をDB保存する。

        Returns:
            None
        """
        self._chat_repository.add_chat_histories(chat_histories)
        self._chat_histories.setdefault(self._chat_key, []).extend(chat_histories)

    async def load_previous_chat_histories(
        self,
        input: ChatRequestModel,
        session_status: ChatSessionStatus | None = None,
        limit: int = 5,
        send_end_response: bool = True,
    ) -> AsyncGenerator[ChatStreamResponseModel, None]:
        """
        指定したメッセージIDより前の会話履歴を取得する

        Args:
            input: ユーザー入力
            session_status: セッションステータス
            limit: 取得件数上限
            send_end_response: 終了レスポンスを送信するかどうか

        Returns:
            会話履歴
        """
        if session_status is None:
            session_status = self._chat_repository.session_status()
            if session_status is None:
                session_status = ChatSessionStatus.CHATTING
        chat_response = ChatStreamResponse(
            request_type=input.request_type,
            position_id=input.position_id,
            session_status=session_status,
        )

        chat_key = (
            self._position_repository.decrypt_id(input.position_id)
            if input.position_id
            else MAIN_CHAT_KEY
        )
        histories = self._chat_histories.get(chat_key)
        if not histories:
            yield chat_response.create_load_more_response(
                no_more_message=True,
                session_status=session_status,
                end=False,
            )
            return

        if input.current_message_id:
            # フロントへ渡した一番古いメッセージ（current_message_id）を探す
            stop_index = None
            for index in range(len(histories) - 1, -1, -1):
                history = histories[index]
                if history.message_id == input.current_message_id:
                    stop_index = index
                    break

            # 見つからなかった場合は空のリストを返す
            if stop_index is None:
                yield chat_response.create_end_response(
                    session_status,
                )
                return
        else:
            stop_index = len(histories)

        # stop_indexより前の履歴取得
        previous_non_user_message_found = False
        for index in range(stop_index - 1, -1, -1):
            if not previous_non_user_message_found and histories[index].role in [
                LLMMessageRole.ASSISTANT,
                LLMMessageRole.TOOL,
            ]:
                previous_non_user_message_found = True

            if previous_non_user_message_found:
                history = histories[index]
                if history.role == LLMMessageRole.ASSISTANT:
                    if history.message_id.startswith(
                        POSITION_CHAT_DETAIL_MESSAGE_ID_PREFIX
                    ):
                        # ポジションチャットサマリ除外
                        continue

                    yield chat_response.create_agent_message_response(
                        history.message_id,
                        history.content,
                        session_status,
                    )
                elif history.role == LLMMessageRole.USER:
                    yield chat_response.create_user_message_response(
                        history.message_id,
                        history.content,
                        session_status,
                    )
                elif (
                    history.role == LLMMessageRole.TOOL
                    and history.tool_name == ToolName.POSITION_SEARCH
                ):
                    parsed_output = self._parse_tool_output(
                        history.content,
                    )

                    if "Message" not in parsed_output:
                        # 成功したポジション検索
                        tool_call_id = history.tool_call_id
                        tool_input = history.tool_input
                        salary = tool_input.get("Salary")
                        locations = tool_input.get("Locations")
                        if not tool_call_id or not salary or not locations:
                            self.logger.error(
                                f"ポジション検索条件が正しくありません。",
                                extra={
                                    "tool_call_id": tool_call_id,
                                    "tool_input": tool_input,
                                },
                            )
                            continue

                        residence = ""
                        work_locations: list[str] = []
                        is_full_remote = tool_input.get("FullyRemoteWork", False)
                        for location in locations:
                            if location["LocationType"] == LocationType.RESIDENCE:
                                residence = (
                                    location["PrefectureName"] + location["CityName"]
                                )
                            elif location["LocationType"] == LocationType.FULL_REMOTE:
                                is_full_remote = True
                            elif location["LocationType"] == LocationType.WORK_LOCATION:
                                work_locations.append(
                                    location["PrefectureName"] + location["CityName"]
                                )
                            else:
                                self.logger.error(
                                    f"不明なロケーションタイプです。",
                                    extra={
                                        "tool_call_id": tool_call_id,
                                        "tool_input": tool_input,
                                    },
                                )
                                continue

                        position_keyword = tool_input.get("PositionKeyword", "")
                        jobtype_names = tool_input.get("JobtypeNames", [])
                        industry_sentence = tool_input.get("IndustrySentence", "")

                        yield chat_response.create_tool_result_link_response(
                            history.message_id,
                            {
                                "ToolCallId": tool_call_id,
                                "Salary": salary,
                                "Residence": residence,
                                "WorkLocations": work_locations,
                                "IsFullyRemoteWork": is_full_remote,
                                "PositionKeyword": position_keyword,
                                "JobtypeNames": jobtype_names,
                                "IndustrySentence": industry_sentence,
                            },
                            session_status,
                        )
                else:
                    continue

                if history.role == LLMMessageRole.USER:
                    limit -= 1
                if limit <= 0:
                    break

        response = chat_response.create_load_more_response(
            limit > 0,
            session_status,
            send_end_response,
        )
        if response:
            yield response

    def _find_last_non_position_guide_agent(self):
        """
        メインチャット履歴から最後のPOSITION_GUIDE以外のactive_agentを返す。
        """
        histories = self._chat_histories.get(MAIN_CHAT_KEY, [])
        for history in reversed(histories):
            if history.active_agent != AgentName.POSITION_GUIDE:
                return history.active_agent
        raise ValueError("POSITION_GUIDE以外のActive Agentが履歴に見つかりませんでした")
