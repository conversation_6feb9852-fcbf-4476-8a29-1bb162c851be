## 共通
TZ=Asia/Tokyo
AICA_ENV=staging
## Agent
AICA_AGENT_API_ENDPOINT=http://internal.lb.stg.miidas.local:9107/aica/mcptool/
AICA_AGENT_DB_HOST=writer.aicadb.stg.miidas.local
AICA_AGENT_DB_NAME=aica
AICA_AGENT_DB_PORT=5432
AICA_AGENT_DB_SSLMODE=require
AICA_AGENT_DB_USER=aica
AICA_AGENT_MCP_ENDPOINT=http://internal.lb.stg.miidas.local:9106/sse
AICA_AGENT_PORT=80
AICA_AGENT_MAINTENANCE_MODE=0
AICA_AGENT_MIIDAS_API_ENDPOINT=https://stg.miidas.dev/
# stg環境ではトレース・ログ共に有効化したい
# OPENAI_AGENTS_DISABLE_TRACING=1
# OPENAI_AGENTS_DONT_LOG_MODEL_DATA=1
## API
AICA_MCP_TOOL_API_DB_HOST=writer.aicadb.stg.miidas.local
AICA_MCP_TOOL_API_DB_NAME=aica
AICA_MCP_TOOL_API_DB_PORT=5432
AICA_MCP_TOOL_API_DB_SSLMODE=require
AICA_MCP_TOOL_API_DB_USER=aica
AICA_MCP_TOOL_API_MIIDAS_DB_HOST=main.reader.db.stg.miidas.local
AICA_MCP_TOOL_API_MIIDAS_DB_PORT=3306
AICA_MCP_TOOL_API_MIIDAS_DB_USER=user_user_apply_api
AICA_MCP_TOOL_API_MV2_HOST=internal-lb.stg.miidas.dev
AICA_MCP_TOOL_API_MV2_INSECURE=false
AICA_MCP_TOOL_API_MV2_PORT=9102
AICA_MCP_TOOL_API_PORT=9107
AICA_MCP_TOOL_BATCH_DB_HOST=writer.aicadb.stg.miidas.local
AICA_MCP_TOOL_BATCH_DB_NAME=aica
AICA_MCP_TOOL_BATCH_DB_PORT=5432
AICA_MCP_TOOL_BATCH_DB_SSLMODE=require
AICA_MCP_TOOL_BATCH_DB_USER=aica
AICA_MCP_TOOL_BATCH_MIIDAS_DB_HOST=main.reader.db.stg.miidas.local
AICA_MCP_TOOL_BATCH_MIIDAS_DB_PORT=3306
AICA_MCP_TOOL_BATCH_MIIDAS_DB_USER=user_user_apply_batch
MIIDAS_S3_USER_ASSETS_ENDPOINT=assets.stg.miidas.dev
## MCP
AICA_MCP_API_SERVER=http://internal.lb.stg.miidas.local:9107
AICA_MCP_DB_HOST=reader.aicadb.stg.miidas.local
AICA_MCP_DB_NAME=aica
AICA_MCP_DB_PORT=5432
AICA_MCP_DB_SSLMODE=require
AICA_MCP_DB_USER=aica
AICA_MCP_PORT=8080
